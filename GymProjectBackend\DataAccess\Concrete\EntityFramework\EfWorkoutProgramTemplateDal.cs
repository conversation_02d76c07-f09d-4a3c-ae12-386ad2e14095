using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfWorkoutProgramTemplateDal : EfCompanyEntityRepositoryBase<WorkoutProgramTemplate, GymContext>, IWorkoutProgramTemplateDal
    {
        private readonly ICompanyContext _companyContext;

        public EfWorkoutProgramTemplateDal(ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        public List<WorkoutProgramTemplateListDto> GetWorkoutProgramTemplateList()
        {
            using (GymContext context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();

                var result = from wpt in context.WorkoutProgramTemplates
                             where wpt.CompanyID == companyId && wpt.IsActive == true
                             select new WorkoutProgramTemplateListDto
                             {
                                 WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                                 ProgramName = wpt.ProgramName,
                                 Description = wpt.Description,
                                 ExperienceLevel = wpt.ExperienceLevel,
                                 TargetGoal = wpt.TargetGoal,
                                 IsActive = wpt.IsActive,
                                 CreationDate = wpt.CreationDate,
                                 DayCount = context.WorkoutProgramDays
                                     .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                     .Count(),
                                 ExerciseCount = context.WorkoutProgramExercises
                                     .Where(e => context.WorkoutProgramDays
                                         .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                         .Select(d => d.WorkoutProgramDayID)
                                         .Contains(e.WorkoutProgramDayID))
                                     .Count()
                             };

                return result.OrderByDescending(x => x.CreationDate).ToList();
            }
        }

        public WorkoutProgramTemplateDto GetWorkoutProgramTemplateDetail(int templateId)
        {
            using (GymContext context = new GymContext())
            {
                int companyId = _companyContext.GetCompanyId();

                var template = context.WorkoutProgramTemplates
                    .Where(wpt => wpt.WorkoutProgramTemplateID == templateId && wpt.CompanyID == companyId)
                    .Select(wpt => new WorkoutProgramTemplateDto
                    {
                        WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                        CompanyID = wpt.CompanyID,
                        ProgramName = wpt.ProgramName,
                        Description = wpt.Description,
                        ExperienceLevel = wpt.ExperienceLevel,
                        TargetGoal = wpt.TargetGoal,
                        IsActive = wpt.IsActive,
                        CreationDate = wpt.CreationDate,
                        DayCount = context.WorkoutProgramDays
                            .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                            .Count()
                    })
                    .FirstOrDefault();

                if (template != null)
                {
                    // Günleri getir
                    template.Days = GetWorkoutProgramDays(context, templateId);
                }

                return template;
            }
        }

        private List<WorkoutProgramDayDto> GetWorkoutProgramDays(GymContext context, int templateId)
        {
            var days = context.WorkoutProgramDays
                .Where(d => d.WorkoutProgramTemplateID == templateId)
                .Select(d => new WorkoutProgramDayDto
                {
                    WorkoutProgramDayID = d.WorkoutProgramDayID,
                    WorkoutProgramTemplateID = d.WorkoutProgramTemplateID,
                    DayNumber = d.DayNumber,
                    DayName = d.DayName,
                    IsRestDay = d.IsRestDay,
                    CreationDate = d.CreationDate
                })
                .OrderBy(d => d.DayNumber)
                .ToList();

            // Her gün için egzersizleri getir
            foreach (var day in days)
            {
                day.Exercises = GetWorkoutProgramExercises(context, day.WorkoutProgramDayID);
            }

            return days;
        }

        private List<WorkoutProgramExerciseDto> GetWorkoutProgramExercises(GymContext context, int dayId)
        {
            var exercises = from wpe in context.WorkoutProgramExercises
                           where wpe.WorkoutProgramDayID == dayId
                           select new WorkoutProgramExerciseDto
                           {
                               WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                               WorkoutProgramDayID = wpe.WorkoutProgramDayID,
                               ExerciseType = wpe.ExerciseType,
                               ExerciseID = wpe.ExerciseID,
                               OrderIndex = wpe.OrderIndex,
                               Sets = wpe.Sets,
                               Reps = wpe.Reps,
                               RestTime = wpe.RestTime,
                               Notes = wpe.Notes,
                               CreationDate = wpe.CreationDate
                           };

            var result = exercises.OrderBy(e => e.OrderIndex).ToList();

            // Egzersiz isimlerini getir
            foreach (var exercise in result)
            {
                if (exercise.ExerciseType == "System")
                {
                    var systemExercise = context.SystemExercises
                        .Where(se => se.SystemExerciseID == exercise.ExerciseID)
                        .Select(se => new { se.ExerciseName, se.Description, CategoryName = context.ExerciseCategories.Where(ec => ec.ExerciseCategoryID == se.ExerciseCategoryID).Select(ec => ec.CategoryName).FirstOrDefault() })
                        .FirstOrDefault();

                    if (systemExercise != null)
                    {
                        exercise.ExerciseName = systemExercise.ExerciseName;
                        exercise.ExerciseDescription = systemExercise.Description;
                        exercise.CategoryName = systemExercise.CategoryName;
                    }
                }
                else if (exercise.ExerciseType == "Company")
                {
                    var companyExercise = context.CompanyExercises
                        .Where(ce => ce.CompanyExerciseID == exercise.ExerciseID)
                        .Select(ce => new { ce.ExerciseName, ce.Description, CategoryName = context.ExerciseCategories.Where(ec => ec.ExerciseCategoryID == ce.ExerciseCategoryID).Select(ec => ec.CategoryName).FirstOrDefault() })
                        .FirstOrDefault();

                    if (companyExercise != null)
                    {
                        exercise.ExerciseName = companyExercise.ExerciseName;
                        exercise.ExerciseDescription = companyExercise.Description;
                        exercise.CategoryName = companyExercise.CategoryName;
                    }
                }
            }

            return result;
        }
    }
}
