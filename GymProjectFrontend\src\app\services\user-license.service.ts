import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './baseApiService';
import { ListResponseModel } from '../models/listResponseModel';
import { ResponseModel } from '../models/responseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { LicensePurchaseDto } from '../models/LicensePurchaseDto';
import { UserLicense } from '../models/UserLicense';
import { UserLicenseDto } from '../models/UserLicenseDto';
import { PaginatedUserLicenseDto } from '../models/PaginatedUserLicenseDto';

@Injectable({
  providedIn: 'root'
})
export class UserLicenseService extends BaseApiService {
  constructor(private httpClient: HttpClient) {
    super();
  }

  getAll(): Observable<ListResponseModel<UserLicenseDto>> {
    return this.httpClient.get<ListResponseModel<UserLicenseDto>>(this.apiUrl + 'UserLicenses/getall');
  }

  getAllPaginated(
    page: number = 1,
    pageSize: number = 20,
    searchTerm: string = '',
    sortBy: string = 'newest',
    companyName: string = '',
    remainingDaysMin?: number,
    remainingDaysMax?: number
  ): Observable<SingleResponseModel<PaginatedUserLicenseDto>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('pageSize', pageSize.toString())
      .set('searchTerm', searchTerm)
      .set('sortBy', sortBy)
      .set('companyName', companyName);

    if (remainingDaysMin !== undefined && remainingDaysMin !== null) {
      params = params.set('remainingDaysMin', remainingDaysMin.toString());
    }
    if (remainingDaysMax !== undefined && remainingDaysMax !== null) {
      params = params.set('remainingDaysMax', remainingDaysMax.toString());
    }

    return this.httpClient.get<SingleResponseModel<PaginatedUserLicenseDto>>(
      this.apiUrl + 'UserLicenses/getallpaginated',
      { params }
    );
  }

  getExpiredAndPassive(
    page: number = 1,
    pageSize: number = 20,
    searchTerm: string = ''
  ): Observable<SingleResponseModel<PaginatedUserLicenseDto>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('pageSize', pageSize.toString())
      .set('searchTerm', searchTerm);

    return this.httpClient.get<SingleResponseModel<PaginatedUserLicenseDto>>(
      this.apiUrl + 'UserLicenses/getexpiredandpassive',
      { params }
    );
  }

  getById(id: number): Observable<SingleResponseModel<UserLicenseDto>> {
    return this.httpClient.get<SingleResponseModel<UserLicenseDto>>(this.apiUrl + 'UserLicenses/getbyid?id=' + id);
  }

  getActiveByUserId(userId: number): Observable<ListResponseModel<UserLicenseDto>> {
    return this.httpClient.get<ListResponseModel<UserLicenseDto>>(this.apiUrl + 'UserLicenses/getactivebyuserid?userId=' + userId);
  }

  getMyActiveLicenses(): Observable<ListResponseModel<UserLicenseDto>> {
    return this.httpClient.get<ListResponseModel<UserLicenseDto>>(this.apiUrl + 'UserLicenses/getmyactivelicenses');
  }

  getUserRoles(userId: number): Observable<ListResponseModel<string>> {
    return this.httpClient.get<ListResponseModel<string>>(this.apiUrl + 'UserLicenses/getuserroles?userId=' + userId);
  }

  add(userLicense: UserLicense): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(this.apiUrl + 'UserLicenses/add', userLicense);
  }

  update(userLicense: UserLicense): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(this.apiUrl + 'UserLicenses/update', userLicense);
  }

  delete(id: number): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(this.apiUrl + 'UserLicenses/delete?id=' + id);
  }

  purchase(licensePurchaseDto: LicensePurchaseDto): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(this.apiUrl + 'UserLicenses/purchase', licensePurchaseDto);
  }

  extendLicense(userLicenseId: number, extensionDays: number): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(this.apiUrl + 'UserLicenses/extend', null, {
      params: {
        userLicenseId: userLicenseId.toString(),
        extensionDays: extensionDays.toString()
      }
    });
  }

  extendLicenseByPackage(userLicenseId: number, licensePackageId: number): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(this.apiUrl + 'UserLicenses/extendbypackage', null, {
      params: {
        userLicenseId: userLicenseId.toString(),
        licensePackageId: licensePackageId.toString()
      }
    });
  }

  revokeLicense(userLicenseId: number): Observable<ResponseModel> {
    return this.httpClient.post<ResponseModel>(this.apiUrl + 'UserLicenses/revoke', null, {
      params: { userLicenseId: userLicenseId.toString() }
    });
  }
}
