import { Component, OnInit, OnDestroy } from '@angular/core';
import { CompanyUserFullDetail } from '../../models/companyUserFullDetail';
import { CompanyUserService } from '../../services/company-user.service';
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { faEdit, faTrashAlt, faInfoCircle, faSyncAlt, faUserPlus, faBuilding } from '@fortawesome/free-solid-svg-icons';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { CompanyUserDetailDialogComponent } from '../company-user-detail-dialog/company-user-detail-dialog.component';

@Component({
    selector: 'app-company-user-details',
    templateUrl: './company-user-details.component.html',
    styleUrls: ['./company-user-details.component.css'],
    standalone: false
})
export class CompanyUserDetailsComponent implements OnInit, OnDestroy {
  companyUsers: CompanyUserFullDetail[] = [];
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  totalCompanyUsers = 0;
  searchText = '';
  isLoading = false;
  viewMode = 'table'; // 'table' or 'card'

  // Icons
  faTrashAlt = faTrashAlt;
  faEdit = faEdit;
  faInfoCircle = faInfoCircle;
  faSyncAlt = faSyncAlt;
  faUserPlus = faUserPlus;
  faBuilding = faBuilding;

  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  constructor(
    private companyUserService: CompanyUserService,
    private toastrService: ToastrService,
    private dialog: MatDialog
  ) {
    // Debounced search setup
    this.searchSubject.pipe(
      debounceTime(300),
      takeUntil(this.destroy$)
    ).subscribe(searchText => {
      this.searchText = searchText;
      this.currentPage = 1;
      this.loadCompanyUsers();
    });
  }

  ngOnInit(): void {
    this.loadCompanyUsers();
    this.loadTotalCompanyUsers();

    // Load saved view mode preference
    const savedViewMode = localStorage.getItem('companyUserViewMode');
    if (savedViewMode) {
      this.viewMode = savedViewMode;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadTotalCompanyUsers(): void {
    this.companyUserService.getCompanyUsers().subscribe({
      next: (response) => {
        if (response.success) {
          this.totalCompanyUsers = response.data.length;
        }
      },
      error: (error) => {
        console.error('Error fetching total company users:', error);
      }
    });
  }

  loadCompanyUsers() {
    this.isLoading = true;

    this.companyUserService.getPaginated(this.currentPage, 10, this.searchText).subscribe({
      next: (response) => {
        if (response.success) {
          this.companyUsers = response.data.data;
          this.totalPages = response.data.totalPages;
          this.totalItems = response.data.totalCount;
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching company users:', error);
        this.toastrService.error('Şirket kullanıcıları yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
      }
    });
  }

  onSearch(event: any): void {
    const searchValue = event.target.value;
    this.searchSubject.next(searchValue);
  }



  onPageChange(page: number) {
    this.currentPage = page;
    this.loadCompanyUsers();
  }

  setViewMode(mode: string) {
    this.viewMode = mode;
    localStorage.setItem('companyUserViewMode', this.viewMode);
  }

  toggleViewMode() {
    this.viewMode = this.viewMode === 'table' ? 'card' : 'table';
    localStorage.setItem('companyUserViewMode', this.viewMode);
  }

  clearSearch() {
    this.searchText = '';
    this.searchSubject.next('');
  }

  viewCompanyUserDetails(companyUser: CompanyUserFullDetail): void {
    // Detaylar butonu - sadece okuma modu
    const dialogRef = this.dialog.open(CompanyUserDetailDialogComponent, {
      width: '95%',
      maxWidth: '1000px',
      height: '85vh',
      maxHeight: '750px',
      data: {
        companyUserId: companyUser.companyUserID,
        readonly: true // Readonly modu aktif
      },
      panelClass: 'company-user-detail-dialog-container'
    });

    // Readonly modda güncelleme olmayacağı için result kontrolü gereksiz
    dialogRef.afterClosed().subscribe();
  }

  editCompanyUser(companyUser: CompanyUserFullDetail): void {
    // Güncelle butonu - düzenleme modu
    const dialogRef = this.dialog.open(CompanyUserDetailDialogComponent, {
      width: '95%',
      maxWidth: '1000px',
      height: '85vh',
      maxHeight: '750px',
      data: {
        companyUserId: companyUser.companyUserID,
        readonly: false // Düzenleme modu aktif
      },
      panelClass: 'company-user-detail-dialog-container'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === 'updated') {
        this.loadCompanyUsers();
        this.loadTotalCompanyUsers();
      }
    });
  }

  deleteCompanyUser(companyUser: CompanyUserFullDetail): void {
    const confirmMessage = `"${companyUser.name}" adlı salon sahibini ve bağlı salon bilgilerini silmek istediğinizden emin misiniz?\n\nBu işlem şunları silecek:\n• Salon sahibi bilgileri\n• Salon bilgileri\n• Salon adresi\n• Kullanıcı hesabı\n\nÜye verileri korunacak ve salon "Silinen Salonlar" bölümünden geri yüklenebilir.`;

    if (confirm(confirmMessage)) {
      this.companyUserService.softDelete(companyUser.companyUserID).subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success(response.message, 'Başarılı');
            this.loadCompanyUsers();
            this.loadTotalCompanyUsers();
          } else {
            this.toastrService.error(response.message, 'Hata');
          }
        },
        error: (error) => {
          console.error('Error deleting company user:', error);
          this.toastrService.error('Salon silinirken bir hata oluştu.', 'Hata');
        }
      });
    }
  }

  getInitials(name: string): string {
    if (!name) return 'CU';
    const words = name.split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  }

  getAvatarColor(name: string): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  }

  getStatusClass(companyUser: CompanyUserFullDetail): string {
    if (!companyUser.isActive) return 'inactive';
    if (!companyUser.userIsActive) return 'inactive';
    if (companyUser.requirePasswordChange) return 'inactive';
    return 'active';
  }

  getStatusText(companyUser: CompanyUserFullDetail): string {
    if (!companyUser.isActive) return 'Pasif';
    if (!companyUser.userIsActive) return 'Kullanıcı Hesabı Pasif';
    if (companyUser.requirePasswordChange) return 'Şifre Değiştirmeli';
    return 'Aktif';
  }
}
