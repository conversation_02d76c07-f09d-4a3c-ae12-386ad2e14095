using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.Concrete
{
    public class Expense : IEntity, ICompanyEntity
    {
        public int ExpenseID { get; set; }
        public int CompanyID { get; set; }
        public string? Description { get; set; } // Nullable yapıldı
        public decimal Amount { get; set; }
        public DateTime ExpenseDate { get; set; }
        public string? ExpenseType { get; set; } // Nullable olabilir
        public DateTime CreationDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public DateTime? DeletedDate { get; set; }
        public bool IsActive { get; set; }
    }
}