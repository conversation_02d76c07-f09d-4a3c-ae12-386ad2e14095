import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ProductService } from '../../services/product.service';
import { Product } from '../../models/product';
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { ProductUpdateComponent } from '../crud/product-update/product-update.component';
import { faEdit, faTrashAlt } from '@fortawesome/free-solid-svg-icons';
import { DialogService } from '../../services/dialog.service';

@Component({
    selector: 'app-product-list',
    templateUrl: './product-list.component.html',
    styleUrls: ['./product-list.component.css'],
    standalone: false
})
export class ProductListComponent implements OnInit {
  products: Product[] = [];
  filteredProducts: Product[] = [];
  productForm: FormGroup;
  faTrashAlt = faTrashAlt;
  faEdit = faEdit;
  isLoading: boolean = false;
  searchTerm: string = '';
  
  // Pagination properties
  currentPage: number = 1;
  itemsPerPage: number = 10;
  totalPages: number = 1;
  
  // Sorting properties
  sortField: string = 'productID';
  sortDirection: string = 'desc';

  constructor(
    private productService: ProductService,
    private toastrService: ToastrService,
    private dialog: MatDialog,
    private fb: FormBuilder,
    private dialogService: DialogService
  ) {}

  ngOnInit(): void {
    this.getProducts();
    this.createProductForm();
  }

  createProductForm() {
    this.productForm = this.fb.group({
      name: ['', Validators.required],
      price: [0, [Validators.required, Validators.min(0)]],
    });
  }
  
  // Filtering and sorting methods
  filterProducts(): void {
    if (!this.searchTerm.trim()) {
      this.filteredProducts = [...this.products];
    } else {
      const term = this.searchTerm.toLowerCase().trim();
      this.filteredProducts = this.products.filter(product => 
        product.name.toLowerCase().includes(term)
      );
    }
    
    this.sortProductsArray();
    this.updatePagination();
  }
  
  sortProducts(field: string, direction: string): void {
    this.sortField = field;
    this.sortDirection = direction;
    this.sortProductsArray();
  }

  getSortText(): string {
    if (this.sortField === 'name') {
      return this.sortDirection === 'asc' ? 'İsim (A-Z)' : 'İsim (Z-A)';
    } else if (this.sortField === 'price') {
      return this.sortDirection === 'asc' ? 'Fiyat (Artan)' : 'Fiyat (Azalan)';
    }
    return 'Sıralama';
  }
  
  private sortProductsArray(): void {
    this.filteredProducts.sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;
      
      // Explicitly handle each property of the Product interface
      if (this.sortField === 'name') {
        aValue = a.name;
        bValue = b.name;
        return this.sortDirection === 'asc' 
          ? aValue.localeCompare(bValue) 
          : bValue.localeCompare(aValue);
      } else if (this.sortField === 'price') {
        aValue = a.price;
        bValue = b.price;
        return this.sortDirection === 'asc' 
          ? aValue - bValue 
          : bValue - aValue;
      } else if (this.sortField === 'productID') {
        aValue = a.productID;
        bValue = b.productID;
        return this.sortDirection === 'asc' 
          ? aValue - bValue 
          : bValue - aValue;
      }
      
      // Default sort by name if field is not recognized
      return this.sortDirection === 'asc' 
        ? a.name.localeCompare(b.name) 
        : b.name.localeCompare(a.name);
    });
  }
  
  // Pagination methods
  updatePagination(): void {
    this.totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);
    this.currentPage = 1;
  }
  
  getPageNumbers(): number[] {
    return Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }
  
  changePage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }
  
  deleteProduct(product: Product) {
    this.dialogService.confirmProductDelete(product.name).subscribe(result => {
      if (result) {
        this.isLoading = true;
        this.productService.deleteProduct(product.productID).subscribe(
          response => {
            this.toastrService.success('Ürün başarıyla silindi');
            this.getProducts();
          },
          error => {
            this.toastrService.error('Ürün silinirken bir hata oluştu');
            this.isLoading = false;
          }
        );
      }
    });
  }
  
  getProducts() {
    this.isLoading = true;
    this.productService.getProducts().subscribe(
      response => {
        this.products = response.data;
        this.filteredProducts = [...this.products];
        this.sortProductsArray(); // Sort products by productID desc
        this.updatePagination();
        this.isLoading = false;
      },
      error => {
        this.toastrService.error('Ürünler yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    );
  }

  addProduct() {
    if (this.productForm.valid) {
      this.isLoading = true;
      
      let productModel = Object.assign({}, this.productForm.value);
      this.productService.addProduct(productModel).subscribe(
        response => {
          this.toastrService.success(response.message, 'Başarılı');
          this.getProducts();
          this.productForm.reset();
        },
        responseError => {
          this.toastrService.error(responseError.error.message, 'Hata');
          this.isLoading = false;
        }
      );
    }
  }

  editProduct(product: Product) {
    const dialogRef = this.dialog.open(ProductUpdateComponent, {
      width: '400px',
      data: product
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.isLoading = true;
        this.productService.updateProduct(result).subscribe(
          response => {
            this.toastrService.success(response.message, 'Başarılı');
            this.getProducts();
          },
          responseError => {
            this.toastrService.error(responseError.error.message, 'Hata');
            this.isLoading = false;
          }
        );
      }
    });
  }
}
