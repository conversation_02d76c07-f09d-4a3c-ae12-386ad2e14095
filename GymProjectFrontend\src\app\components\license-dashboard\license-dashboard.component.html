<!-- license-dashboard.component.html -->
<div class="container mt-4">
    <h2>Lisans Yönetim Paneli</h2>
  
    <div *ngIf="isLoading" class="d-flex justify-content-center my-5">
      <app-loading-spinner></app-loading-spinner>
    </div>
  
    <div *ngIf="!isLoading" class="dashboard-content">
      <!-- Statistics Cards -->
      <div class="row mt-4">
        <div class="col-md-3">
          <div class="card bg-primary text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Aktif Lisanslar</h5>
                  <h2 class="mt-2 mb-0">{{ totalActiveLicenses }}</h2>
                </div>
                <i class="fas fa-certificate fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>
  
        <div class="col-md-3">
          <div class="card bg-warning text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Yakında Sona Erecek</h5>
                  <h2 class="mt-2 mb-0">{{ expiringLicenses }}</h2>
                </div>
                <i class="fas fa-exclamation-triangle fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>
  
        <div class="col-md-3">
          <div class="card bg-success text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Toplam Gelir</h5>
                  <h2 class="mt-2 mb-0">{{ totalRevenue | currency:'TRY':'symbol':'1.0-0' }}</h2>
                </div>
                <i class="fas fa-money-bill-wave fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>
  
        <div class="col-md-3">
          <div class="card bg-info text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Son İşlemler</h5>
                  <h2 class="mt-2 mb-0">{{ recentTransactions.length }}</h2>
                </div>
                <i class="fas fa-history fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Your Licenses Section -->
      <div class="row mt-4" *ngIf="currentUser">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h4>Lisanslarınız</h4>
            </div>
            <div class="card-body">
              <div *ngIf="userLicenses.length === 0" class="alert alert-info">
                Henüz lisans satın almadınız.
              </div>
  
              <div *ngIf="userLicenses.length > 0" class="table-responsive">
                <table class="table table-bordered table-striped">
                  <thead>
                    <tr>
                      <th>Paket</th>
                      <th>Rol</th>
                      <th>Başlangıç Tarihi</th>
                      <th>Bitiş Tarihi</th>
                      <th>Kalan Gün</th>
                      <th>Durum</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let license of userLicenses">
                      <td>{{ license.packageName }}</td>
                      <td>{{ license.role }}</td>
                      <td>{{ license.startDate | date:'dd/MM/yyyy' }}</td>
                      <td>{{ license.endDate | date:'dd/MM/yyyy' }}</td>
                      <td [ngClass]="getRemainingDaysClass(license.remainingDays)">
                        {{ license.remainingDays }} gün
                      </td>
                      <td>
                        <span [class]="getBadgeClass(license.remainingDays)">
                          {{ getLicenseStatus(license.remainingDays) }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Expiring Licenses Section -->
      <div class="row mt-4">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h4>Yakında Sona Erecek Lisanslar</h4>
            </div>
            <div class="card-body">
              <div *ngIf="!hasExpiringLicenses" class="alert alert-info">
                Yakında sona erecek lisans bulunmamaktadır.
              </div>
  
              <div *ngIf="hasExpiringLicenses" class="table-responsive">
                <table class="table table-bordered">
                  <thead>
                    <tr>
                      <th>Kullanıcı</th>
                      <th>Paket</th>
                      <th>Kalan Gün</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let license of expiringLicensesList">
                      <td>{{ license.userName }}</td>
                      <td>{{ license.packageName }}</td>
                      <td class="text-warning">{{ license.remainingDays }} gün</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
  
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h4>Son İşlemler</h4>
            </div>
            <div class="card-body">
              <div *ngIf="recentTransactions.length === 0" class="alert alert-info">
                İşlem bulunmamaktadır.
              </div>
  
              <div *ngIf="recentTransactions.length > 0" class="table-responsive">
                <table class="table table-bordered">
                  <thead>
                    <tr>
                      <th>Tarih</th>
                      <th>Tutar</th>
                      <th>Ödeme Yöntemi</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let transaction of recentTransactions">
                      <td>{{ transaction.transactionDate | date:'dd/MM/yyyy HH:mm' }}</td>
                      <td>{{ transaction.amount | currency:'TRY':'symbol':'1.2-2' }}</td>
                      <td>{{ transaction.paymentMethod }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Quick Actions -->
      <div class="row mt-4">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h4>Hızlı İşlemler</h4>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-3">
                  <a routerLink="/license-packages" class="btn btn-primary btn-block d-flex align-items-center justify-content-between p-3">
                    <span>Lisans Paketleri</span>
                    <i class="fas fa-certificate"></i>
                  </a>
                </div>
                <div class="col-md-3">
                  <a routerLink="/user-licenses" class="btn btn-success btn-block d-flex align-items-center justify-content-between p-3">
                    <span>Aktif Lisanslar</span>
                    <i class="fas fa-user-tag"></i>
                  </a>
                </div>
                <div class="col-md-3">
                  <a routerLink="/expired-licenses" class="btn btn-warning btn-block d-flex align-items-center justify-content-between p-3">
                    <span>Pasif/Süresi Dolan</span>
                    <i class="fas fa-exclamation-triangle"></i>
                  </a>
                </div>
                <div class="col-md-3">
                  <a routerLink="/license-transactions" class="btn btn-info btn-block d-flex align-items-center justify-content-between p-3">
                    <span>İşlem Geçmişi</span>
                    <i class="fas fa-history"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>