﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class TransactionDetailDto
    {
        public int TransactionID { get; set; }
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public int? ProductID { get; set; }
        public string ProductName { get; set; }
        public decimal Amount { get; set; }
        public decimal UnitPrice { get; set; }
        public string TransactionType { get; set; }
        public DateTime TransactionDate { get; set; }
        public int Quantity { get; set; }
        public bool IsPaid { get; set; }
        public decimal Balance { get; set; }
        public decimal TotalPrice { get; set; } 

    }
}
