<div class="container-fluid mt-4">
  <div *ngIf="isLoading" class="d-flex justify-content-center align-items-center" style="height: 100vh;">
    <app-loading-spinner></app-loading-spinner>
  </div>
  
  <div class="fade-in" [class.content-blur]="isLoading">


    <div class="row">
      <!-- <PERSON>r<PERSON>n Ekleme Formu -->
      <div class="col-md-4">
        <div class="modern-card slide-in-left">
          <div class="modern-card-header">
            <h5>Yeni <PERSON><PERSON><PERSON></h5>
          </div>
          <div class="modern-card-body">
            <form [formGroup]="productForm" (ngSubmit)="addProduct()">
              <div class="modern-form-group">
                <div class="row align-items-center mb-2">
                  <div class="col-3">
                    <label for="name" class="modern-form-label mb-0">Ür<PERSON>n <PERSON></label>
                  </div>
                  <div class="col-9">
                    <div class="d-flex align-items-center">
                      <div class="input-group-text me-2" style="height: 38px; width: 38px; justify-content: center;">
                        <i class="fas fa-box"></i>
                      </div>
                      <input type="text" class="modern-form-control" id="name" formControlName="name" placeholder="Ürün adı giriniz" style="width: calc(100% - 50px);">
                    </div>
                  </div>
                </div>
              </div>
              <div class="modern-form-group">
                <div class="row align-items-center">
                  <div class="col-3">
                    <label for="price" class="modern-form-label mb-0">Fiyat</label>
                  </div>
                  <div class="col-9">
                    <div class="d-flex align-items-center">
                      <div class="input-group-text me-2" style="height: 38px; width: 38px; justify-content: center;">
                        <i class="fas fa-money-bill-wave"></i>
                      </div>
                      <input type="number" class="modern-form-control" id="price" formControlName="price" placeholder="0.00" style="width: calc(100% - 50px);">
                    </div>
                  </div>
                </div>
              </div>
              
              <button type="submit" class="modern-btn modern-btn-primary w-100" [disabled]="!productForm.valid">
                <i class="fas fa-plus-circle modern-btn-icon"></i> Ürün Ekle
              </button>
            </form>
          </div>
        </div>
      </div>

      <!-- Ürün Listesi -->
      <div class="col-md-8">
        <div class="modern-card slide-in-right">
          <div class="modern-card-header">
            <h5>Ürün Listesi</h5>
            <div class="d-flex align-items-center">
              <div class="position-relative me-2">
                <input type="text" class="modern-form-control" placeholder="Ürün ara..." [(ngModel)]="searchTerm" (input)="filterProducts()">
                <i class="fas fa-search" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%);"></i>
              </div>
              <div class="dropdown">
                <button class="modern-sort-btn dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                  <i class="fas fa-sort me-2"></i>
                  <span class="sort-text">{{getSortText()}}</span>
                  <span class="sort-badge">{{filteredProducts.length}}</span>
                </button>
                <ul class="dropdown-menu modern-sort-dropdown" aria-labelledby="sortDropdown">
                  <li>
                    <a class="dropdown-item modern-sort-item"
                       [class.active]="sortField === 'name' && sortDirection === 'asc'"
                       (click)="sortProducts('name', 'asc')">
                      <i class="fas fa-sort-alpha-down me-2"></i>
                      <span>İsim (A-Z)</span>
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item modern-sort-item"
                       [class.active]="sortField === 'name' && sortDirection === 'desc'"
                       (click)="sortProducts('name', 'desc')">
                      <i class="fas fa-sort-alpha-up me-2"></i>
                      <span>İsim (Z-A)</span>
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item modern-sort-item"
                       [class.active]="sortField === 'price' && sortDirection === 'asc'"
                       (click)="sortProducts('price', 'asc')">
                      <i class="fas fa-sort-numeric-down me-2 text-success"></i>
                      <span>Fiyat (Artan)</span>
                    </a>
                  </li>
                  <li>
                    <a class="dropdown-item modern-sort-item"
                       [class.active]="sortField === 'price' && sortDirection === 'desc'"
                       (click)="sortProducts('price', 'desc')">
                      <i class="fas fa-sort-numeric-up me-2 text-danger"></i>
                      <span>Fiyat (Azalan)</span>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="modern-card-body">
            <div class="table-responsive">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>Ürün Adı</th>
                    <th>Fiyat</th>
                    <th>İşlem</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let product of filteredProducts" class="fade-in">
                    <td>{{ product.name }}</td>
                    <td>{{ product.price | currency:'TRY':'symbol-narrow':'1.2-2' }}</td>
                    <td>
                      <button class="modern-btn modern-btn-primary modern-btn-sm me-2" (click)="editProduct(product)">
                        <fa-icon [icon]="faEdit"></fa-icon> Düzenle
                      </button>
                      <button class="modern-btn modern-btn-danger modern-btn-sm" (click)="deleteProduct(product)">
                        <fa-icon [icon]="faTrashAlt"></fa-icon> Sil
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
              
              <!-- Sonuç bulunamadı mesajı -->
              <div *ngIf="filteredProducts.length === 0" class="text-center py-5">
                <div class="text-muted">
                  <i class="fas fa-search fa-3x mb-3"></i>
                  <p>Ürün bulunamadı</p>
                </div>
              </div>
            </div>
          </div>
          <div class="modern-card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <span class="modern-badge modern-badge-primary">Toplam: {{filteredProducts.length}} ürün</span>
              </div>
              <nav *ngIf="totalPages > 1">
                <ul class="modern-pagination">
                  <li class="modern-page-item" [class.disabled]="currentPage === 1">
                    <a class="modern-page-link" (click)="changePage(currentPage - 1)" aria-label="Previous">
                      <span aria-hidden="true">&laquo;</span>
                    </a>
                  </li>
                  <li class="modern-page-item" *ngFor="let page of getPageNumbers()" [class.active]="page === currentPage">
                    <a class="modern-page-link" (click)="changePage(page)">{{page}}</a>
                  </li>
                  <li class="modern-page-item" [class.disabled]="currentPage === totalPages">
                    <a class="modern-page-link" (click)="changePage(currentPage + 1)" aria-label="Next">
                      <span aria-hidden="true">&raquo;</span>
                    </a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
