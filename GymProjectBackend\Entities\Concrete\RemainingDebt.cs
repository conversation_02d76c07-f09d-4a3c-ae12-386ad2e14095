﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.Concrete
{
    public class RemainingDebt : ICompanyEntity
    {
        [Key]
        public int RemainingDebtID { get; set; }
        public int PaymentID { get; set; }
        public int CompanyID { get; set; }
        public decimal OriginalAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public DateTime LastUpdateDate { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreationDate { get; set; }
    }

}
