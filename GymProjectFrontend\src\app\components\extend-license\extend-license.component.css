/* Extend License Dialog - Simplified Modern Design */

/* Dialog Container */
.extend-license-dialog {
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

/* Simplified User Info */
.user-info-simple {
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

/* Modern Radio Group */
.modern-radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.modern-radio {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: var(--bg-primary);
}

.modern-radio:hover {
  border-color: var(--primary);
  background-color: var(--primary-light);
}

.modern-radio input[type="radio"] {
  display: none;
}

.modern-radio input[type="radio"]:checked + .radio-mark {
  background-color: var(--primary);
  border-color: var(--primary);
}

.modern-radio input[type="radio"]:checked + .radio-mark::after {
  opacity: 1;
}

.modern-radio input[type="radio"]:checked ~ .radio-content strong {
  color: var(--primary);
}

.radio-mark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  background-color: var(--bg-primary);
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.radio-mark::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.radio-content {
  flex: 1;
}

.radio-content strong {
  display: block;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

/* Package Selection - Simple Select Style */
.select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.select-wrapper .input-icon {
  position: absolute;
  left: 1rem;
  color: var(--text-secondary);
  z-index: 2;
  pointer-events: none;
}

.modern-select {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
}

.modern-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* Package Info */
.package-info {
  margin-top: 1rem;
}

.info-card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: 1rem;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.info-header h6 {
  color: var(--text-primary);
  font-weight: 600;
  margin: 0;
}

.price-badge {
  background-color: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius-sm);
  font-weight: 600;
  font-size: 0.875rem;
}

.info-details {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 0.75rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.description {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.empty-state-small {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
}

/* Modern Input Group */
.modern-input-group {
  display: flex;
  align-items: stretch;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  border: 2px solid var(--border-color);
  transition: border-color 0.3s ease;
}

.modern-input-group:focus-within {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.modern-input-group .modern-input {
  border: none;
  border-radius: 0;
  flex: 1;
}

.modern-input-group .modern-input:focus {
  box-shadow: none;
}

.modern-input-suffix {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  border-left: 1px solid var(--border-color);
}

/* Modern Label */
.modern-label {
  display: block;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Form Error */
.form-error {
  display: flex;
  align-items: center;
  color: var(--danger);
  font-size: 0.8125rem;
  margin-top: 0.5rem;
}

/* Form Section */
.form-section {
  margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .extend-license-dialog {
    max-width: 95vw;
    margin: 1rem;
  }

  .user-info-simple .d-flex {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .modern-radio {
    padding: 0.75rem;
  }

  .info-details {
    flex-direction: column;
    gap: 0.75rem;
  }
}

/* Animation */
.extend-license-dialog {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}





