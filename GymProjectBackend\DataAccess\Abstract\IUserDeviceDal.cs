﻿using Core.DataAccess;
using Core.Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IUserDeviceDal: IEntityRepository<UserDevice>
    {
        List<UserDevice> GetActiveDevicesByUserId(int userId);
        UserDevice GetByRefreshToken(string refreshToken);
    }
}
