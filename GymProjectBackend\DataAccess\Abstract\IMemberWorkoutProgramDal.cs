using Core.DataAccess;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;

namespace DataAccess.Abstract
{
    public interface IMemberWorkoutProgramDal : IEntityRepository<MemberWorkoutProgram>
    {
        /// <summary>
        /// Şirket bazlı tüm program atamalarını getirir (admin paneli için)
        /// </summary>
        List<MemberWorkoutProgramListDto> GetCompanyAssignments(int companyId);

        /// <summary>
        /// Belirli üyenin aktif programlarını getirir
        /// </summary>
        List<MemberWorkoutProgramDto> GetMemberActivePrograms(int memberId);

        /// <summary>
        /// Belirli üyenin program geçmişini getirir
        /// </summary>
        List<MemberWorkoutProgramHistoryDto> GetMemberProgramHistory(int memberId);

        /// <summary>
        /// User ID'ye göre aktif programları getirir (mobil API için)
        /// </summary>
        List<MemberActiveWorkoutProgramDto> GetActiveWorkoutProgramsByUserId(int userId);

        /// <summary>
        /// Program atama detayını getirir
        /// </summary>
        MemberWorkoutProgramDto GetAssignmentDetail(int assignmentId);

        /// <summary>
        /// Belirli programa atanan üye sayısını getirir
        /// </summary>
        int GetAssignedMemberCount(int workoutProgramTemplateId);

        /// <summary>
        /// Şirket bazlı aktif atama sayısını getirir
        /// </summary>
        int GetActiveAssignmentCount(int companyId);

        /// <summary>
        /// Üyeye atanan program detayını getirir (mobil API için)
        /// </summary>
        MemberWorkoutProgramDetailDto GetProgramDetailByUser(int userId, int memberWorkoutProgramId);
    }
}
