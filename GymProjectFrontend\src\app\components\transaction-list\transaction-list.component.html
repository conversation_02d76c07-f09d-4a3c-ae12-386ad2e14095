<!-- transaction-list.component.html -->

<div class="container mt-4">
  <div
    class="d-flex justify-content-center align-items-center"
    *ngIf="isLoading"
  >
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="fade-in" [class.content-blur]="isLoading">
    <div class="modern-card">
      <div class="modern-card-header">
        <h5>İşlem Takibi</h5>
        <div class="transaction-filters">
          <!-- <PERSON><PERSON> -->
          <div class="filter-group">
            <div class="search-input-wrapper">
              <i class="fas fa-search search-icon"></i>
              <input
                [formControl]="searchControl"
                class="modern-form-control search-input"
                placeholder="Üye adı ile ara..."
                type="text"
              />
            </div>
          </div>

          <!-- Tarih Filtreleri -->
          <div class="filter-group date-filters">
            <div class="date-input-wrapper">
              <i class="fas fa-calendar-alt date-icon"></i>
              <input
                type="date"
                class="modern-form-control date-input"
                [(ngModel)]="startDate"
                (change)="onDateFilterChange()"
                placeholder="Başlangıç"
              />
            </div>

            <div class="date-input-wrapper">
              <i class="fas fa-calendar-alt date-icon"></i>
              <input
                type="date"
                class="modern-form-control date-input"
                [(ngModel)]="endDate"
                (change)="onDateFilterChange()"
                placeholder="Bitiş"
              />
            </div>

            <button
              class="clear-filter-btn"
              (click)="clearDateFilter()"
              *ngIf="startDate || endDate"
              title="Tarih filtrelerini temizle"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>
      
      <!-- Görünüm Değiştirme -->
      <div class="transaction-view-selector">
        <div class="view-toggle-container">
          <button
            type="button"
            class="view-toggle-btn"
            [class.active]="!showPaidTransactions"
            (click)="toggleTransactionView(false)">
            <i class="fas fa-exclamation-circle"></i>
            <span>Ödenmemiş</span>
            <span class="count-badge">{{unpaidTransactions.length}}</span>
          </button>
          <button
            type="button"
            class="view-toggle-btn"
            [class.active]="showPaidTransactions"
            (click)="toggleTransactionView(true)">
            <i class="fas fa-check-circle"></i>
            <span>Ödenmiş</span>
            <span class="count-badge">{{paidTransactions.length}}</span>
          </button>
        </div>
      </div>

      <div class="modern-card-body">
        <!-- Kompakt İşlem Özeti -->
        <div class="transaction-summary mb-4">
          <div class="summary-stats">
            <div class="stat-item">
              <i class="fas fa-users stat-icon"></i>
              <span class="stat-value">{{transactionsByMember.length}}</span>
              <span class="stat-label">Üye</span>
            </div>
            <div class="stat-item">
              <i class="fas fa-shopping-cart stat-icon"></i>
              <span class="stat-value">{{getTotalTransactionCount()}}</span>
              <span class="stat-label">İşlem</span>
            </div>
            <div class="stat-item" *ngIf="getLastTransactionDate()">
              <i class="fas fa-calendar-alt stat-icon"></i>
              <span class="stat-value">{{getLastTransactionDate() | date:'dd/MM'}}</span>
              <span class="stat-label">Son İşlem</span>
            </div>
          </div>
        </div>

        <div class="table-responsive">
          <div
            class="member-group mb-4 zoom-in"
            *ngFor="let member of transactionsByMember"
          >
            <!-- Üye Başlığı ve Özeti -->
            <div
              class="d-flex justify-content-between align-items-center p-3 mb-2"
              style="background-color: var(--bg-secondary); border-radius: var(--border-radius-md);"
            >
              <h6 class="mb-0">
                <i class="fas fa-user-circle me-2"></i>
                {{ member.memberName }}
              </h6>

              <div class="d-flex align-items-center">
                <!-- Ürün Özeti -->
                <div
                  class="product-summary me-3"
                  *ngIf="member.totalDebt > 0"
                >
                  <div class="d-flex flex-wrap gap-2">
                    <div
                      *ngFor="let product of member.productSummary | keyvalue"
                      class="modern-badge modern-badge-info"
                    >
                      {{ product.key }}: {{ product.value.quantity }} adet
                    </div>
                  </div>
                </div>

                <!-- Toplam Borç ve Ödeme Butonu -->
                <div class="me-3 total-debt-container" *ngIf="member.totalDebt > 0">
                  <span class="total-debt-label">Toplam Borç:</span>
                  <span class="total-debt-amount">
                    {{
                      member.totalDebt
                        | currency : "TRY" : "symbol-narrow" : "1.2-2"
                    }}
                  </span>
                </div>
                <button
                  class="modern-btn modern-btn-primary modern-btn-sm"
                  (click)="updateAllMemberPayments(member.memberId)"
                  *ngIf="member.totalDebt > 0 && !showPaidTransactions"
                >
                  <i class="fas fa-money-bill-wave modern-btn-icon"></i> Tümünü Öde
                </button>
              </div>
            </div>

            <!-- İşlem Tablosu -->
            <table class="modern-table">
              <thead>
                <tr>
                  <th>Ürün</th>
                  <th class="text-center">Adet</th>
                  <th class="text-end">Birim Fiyat</th>
                  <th class="text-end">
                    {{ showPaidTransactions ? "Tutar" : "Kalan Borç" }}
                  </th>
                  <th>İşlem Türü</th>
                  <th>Tarih</th>
                  <th class="text-center">Ödeme Durumu</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let transaction of member.transactions" class="fade-in">
                  <td>{{ transaction.productName || "-" }}</td>
                  <td class="text-center">{{ transaction.quantity }}</td>
                  <td class="text-end">
                    {{
                      transaction.unitPrice
                        | currency : "TRY" : "symbol-narrow" : "1.2-2"
                    }}
                  </td>
                  <td class="text-end">
                    {{
                      showPaidTransactions
                        ? (transaction.totalPrice
                          | currency : "TRY" : "symbol-narrow" : "1.2-2")
                        : (transaction.amount
                          | currency : "TRY" : "symbol-narrow" : "1.2-2")
                    }}
                  </td>
                  <td>
                    <span class="modern-badge" 
                          [ngClass]="{
                            'modern-badge-primary': transaction.transactionType === 'Satış',
                            'modern-badge-success': transaction.transactionType === 'Bakiye Yükleme',
                            'modern-badge-warning': transaction.transactionType === 'İade',
                            'modern-badge-info': transaction.transactionType !== 'Satış' && 
                                                transaction.transactionType !== 'Bakiye Yükleme' && 
                                                transaction.transactionType !== 'İade'
                          }">
                      {{ transaction.transactionType }}
                    </span>
                  </td>
                  <td>
                    {{
                      transaction.transactionDate
                        | date : "dd/MM/yyyy HH:mm:ss"
                    }}
                  </td>
                  <td class="text-center">
                    <span
                      [class]="getStatusClass(transaction)"
                      (click)="
                        !showPaidTransactions &&
                          updatePaymentStatus(transaction)
                      "
                      [style.cursor]="
                        !showPaidTransactions &&
                        transaction.transactionType !== 'Bakiye Yükleme' &&
                        !transaction.isPaid
                          ? 'pointer'
                          : 'default'
                      "
                      [style.pointer-events]="
                        showPaidTransactions ||
                        transaction.transactionType === 'Bakiye Yükleme' ||
                        transaction.isPaid
                          ? 'none'
                          : 'auto'
                      "
                    >
                      {{ getPaymentStatus(transaction) }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Sonuç bulunamadı mesajı -->
          <div
            *ngIf="transactionsByMember.length === 0"
            class="text-center py-5 fade-in"
          >
            <div class="text-muted">
              <i class="fas fa-search fa-3x mb-3"></i>
              <p>
                {{
                  showPaidTransactions
                    ? "Ödenmiş işlem bulunamadı"
                    : "Ödenmemiş işlem bulunamadı"
                }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
