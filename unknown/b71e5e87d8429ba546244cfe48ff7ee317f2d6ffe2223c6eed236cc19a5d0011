﻿using Core.DataAccess;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IPaymentDal: IEntityRepository<Payment>
    {
        List<PaymentHistoryDto> GetPaymentHistory();
        List<PaymentHistoryDto> GetDebtorMembers();
        bool UpdatePaymentStatus(int paymentId, string paymentMethod);
        PaginatedResult<PaymentHistoryDto> GetPaymentHistoryPaginated(PaymentPagingParameters parameters);
        PaymentTotals GetPaymentTotals(PaymentPagingParameters parameters);
        MonthlyRevenueDto GetMonthlyRevenue(int year);
        List<PaymentHistoryDto> GetAllCombinedPaymentHistory(PaymentPagingParameters parameters); // Excel export için eklendi
    }
}
