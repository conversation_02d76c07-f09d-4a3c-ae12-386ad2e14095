{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/keycodes-0e4398c6.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/shadow-dom-318658ae.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/focus-monitor-28b6c826.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/fake-event-detection-84590b88.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/passive-listeners-93cf8be8.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/visually-hidden-9a93b8bb.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/breakpoints-observer-8e7409df.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/array-6239d2f8.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/observe-content-937cdfbe.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/a11y-module-e500b0f2.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/id-generator-0b91c6f7.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/modifiers-3e8908bb.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/list-key-manager-f9c3e90c.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/typeahead-0113d27c.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/activedescendant-key-manager-f0c079ca.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/a11y.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/tree-key-manager-1212bcbe.mjs", "../../../../../../node_modules/@angular/cdk/fesm2022/observable-3cba8a1c.mjs", "../../../../../../node_modules/@angular/material/fesm2022/common-module-5a9c16bb.mjs"], "sourcesContent": ["const MAC_ENTER = 3;\nconst BACKSPACE = 8;\nconst TAB = 9;\nconst NUM_CENTER = 12;\nconst ENTER = 13;\nconst SHIFT = 16;\nconst CONTROL = 17;\nconst ALT = 18;\nconst PAUSE = 19;\nconst CAPS_LOCK = 20;\nconst ESCAPE = 27;\nconst SPACE = 32;\nconst PAGE_UP = 33;\nconst PAGE_DOWN = 34;\nconst END = 35;\nconst HOME = 36;\nconst LEFT_ARROW = 37;\nconst UP_ARROW = 38;\nconst RIGHT_ARROW = 39;\nconst DOWN_ARROW = 40;\nconst PLUS_SIGN = 43;\nconst PRINT_SCREEN = 44;\nconst INSERT = 45;\nconst DELETE = 46;\nconst ZERO = 48;\nconst ONE = 49;\nconst TWO = 50;\nconst THREE = 51;\nconst FOUR = 52;\nconst FIVE = 53;\nconst SIX = 54;\nconst SEVEN = 55;\nconst EIGHT = 56;\nconst NINE = 57;\nconst FF_SEMICOLON = 59; // Firefox (Gecko) fires this for semicolon instead of 186\nconst FF_EQUALS = 61; // Firefox (Gecko) fires this for equals instead of 187\nconst QUESTION_MARK = 63;\nconst AT_SIGN = 64;\nconst A = 65;\nconst B = 66;\nconst C = 67;\nconst D = 68;\nconst E = 69;\nconst F = 70;\nconst G = 71;\nconst H = 72;\nconst I = 73;\nconst J = 74;\nconst K = 75;\nconst L = 76;\nconst M = 77;\nconst N = 78;\nconst O = 79;\nconst P = 80;\nconst Q = 81;\nconst R = 82;\nconst S = 83;\nconst T = 84;\nconst U = 85;\nconst V = 86;\nconst W = 87;\nconst X = 88;\nconst Y = 89;\nconst Z = 90;\nconst META = 91; // WIN_KEY_LEFT\nconst MAC_WK_CMD_LEFT = 91;\nconst MAC_WK_CMD_RIGHT = 93;\nconst CONTEXT_MENU = 93;\nconst NUMPAD_ZERO = 96;\nconst NUMPAD_ONE = 97;\nconst NUMPAD_TWO = 98;\nconst NUMPAD_THREE = 99;\nconst NUMPAD_FOUR = 100;\nconst NUMPAD_FIVE = 101;\nconst NUMPAD_SIX = 102;\nconst NUMPAD_SEVEN = 103;\nconst NUMPAD_EIGHT = 104;\nconst NUMPAD_NINE = 105;\nconst NUMPAD_MULTIPLY = 106;\nconst NUMPAD_PLUS = 107;\nconst NUMPAD_MINUS = 109;\nconst NUMPAD_PERIOD = 110;\nconst NUMPAD_DIVIDE = 111;\nconst F1 = 112;\nconst F2 = 113;\nconst F3 = 114;\nconst F4 = 115;\nconst F5 = 116;\nconst F6 = 117;\nconst F7 = 118;\nconst F8 = 119;\nconst F9 = 120;\nconst F10 = 121;\nconst F11 = 122;\nconst F12 = 123;\nconst NUM_LOCK = 144;\nconst SCROLL_LOCK = 145;\nconst FIRST_MEDIA = 166;\nconst FF_MINUS = 173;\nconst MUTE = 173; // Firefox (Gecko) fires 181 for MUTE\nconst VOLUME_DOWN = 174; // Firefox (Gecko) fires 182 for VOLUME_DOWN\nconst VOLUME_UP = 175; // Firefox (Gecko) fires 183 for VOLUME_UP\nconst FF_MUTE = 181;\nconst FF_VOLUME_DOWN = 182;\nconst LAST_MEDIA = 183;\nconst FF_VOLUME_UP = 183;\nconst SEMICOLON = 186; // Firefox (Gecko) fires 59 for SEMICOLON\nconst EQUALS = 187; // Firefox (Gecko) fires 61 for EQUALS\nconst COMMA = 188;\nconst DASH = 189; // Firefox (Gecko) fires 173 for DASH/MINUS\nconst PERIOD = 190;\nconst SLASH = 191;\nconst APOSTROPHE = 192;\nconst TILDE = 192;\nconst OPEN_SQUARE_BRACKET = 219;\nconst BACKSLASH = 220;\nconst CLOSE_SQUARE_BRACKET = 221;\nconst SINGLE_QUOTE = 222;\nconst MAC_META = 224;\nexport { M as $, ALT as A, BACKSPACE as B, CONTROL as C, DOWN_ARROW as D, END as E, FOUR as F, E as G, HOME as H, INSERT as I, F as J, G as K, LEFT_ARROW as L, MAC_META as M, NINE as N, ONE as O, PAGE_DOWN as P, QUESTION_MARK as Q, RIGHT_ARROW as R, SHIFT as S, TAB as T, UP_ARROW as U, H as V, I as W, J as X, K as Y, Z, L as _, META as a, CLOSE_SQUARE_BRACKET as a$, N as a0, O as a1, P as a2, Q as a3, R as a4, S as a5, T as a6, U as a7, V as a8, W as a9, F7 as aA, F8 as aB, F9 as aC, F10 as aD, F11 as aE, F12 as aF, NUM_LOCK as aG, SCROLL_LOCK as aH, FIRST_MEDIA as aI, FF_MINUS as aJ, MUTE as aK, VOLUME_DOWN as aL, VOLUME_UP as aM, FF_MUTE as aN, FF_VOLUME_DOWN as aO, LAST_MEDIA as aP, FF_VOLUME_UP as aQ, SEMICOLON as aR, EQUALS as aS, COMMA as aT, DASH as aU, PERIOD as aV, SLASH as aW, APOSTROPHE as aX, TILDE as aY, OPEN_SQUARE_BRACKET as aZ, BACKSLASH as a_, X as aa, Y as ab, MAC_WK_CMD_LEFT as ac, MAC_WK_CMD_RIGHT as ad, CONTEXT_MENU as ae, NUMPAD_ZERO as af, NUMPAD_ONE as ag, NUMPAD_TWO as ah, NUMPAD_THREE as ai, NUMPAD_FOUR as aj, NUMPAD_FIVE as ak, NUMPAD_SIX as al, NUMPAD_SEVEN as am, NUMPAD_EIGHT as an, NUMPAD_NINE as ao, NUMPAD_MULTIPLY as ap, NUMPAD_PLUS as aq, NUMPAD_MINUS as ar, NUMPAD_PERIOD as as, NUMPAD_DIVIDE as at, F1 as au, F2 as av, F3 as aw, F4 as ax, F5 as ay, F6 as az, PAGE_UP as b, SINGLE_QUOTE as b0, A as c, ZERO as d, ESCAPE as e, MAC_ENTER as f, NUM_CENTER as g, ENTER as h, PAUSE as i, CAPS_LOCK as j, SPACE as k, PLUS_SIGN as l, PRINT_SCREEN as m, DELETE as n, TWO as o, THREE as p, FIVE as q, SIX as r, SEVEN as s, EIGHT as t, FF_SEMICOLON as u, FF_EQUALS as v, AT_SIGN as w, B as x, C as y, D as z };\n", "let shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\nfunction _supportsShadowDom() {\n  if (shadowDomIsSupported == null) {\n    const head = typeof document !== 'undefined' ? document.head : null;\n    shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n  }\n  return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nfunction _getShadowRoot(element) {\n  if (_supportsShadowDom()) {\n    const rootNode = element.getRootNode ? element.getRootNode() : null;\n    // Note that this should be caught by `_supportsShadowDom`, but some\n    // teams have been able to hit this code path on unsupported browsers.\n    if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n      return rootNode;\n    }\n  }\n  return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nfunction _getFocusedElementPierceShadowDom() {\n  let activeElement = typeof document !== 'undefined' && document ? document.activeElement : null;\n  while (activeElement && activeElement.shadowRoot) {\n    const newActiveElement = activeElement.shadowRoot.activeElement;\n    if (newActiveElement === activeElement) {\n      break;\n    } else {\n      activeElement = newActiveElement;\n    }\n  }\n  return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\nfunction _getEventTarget(event) {\n  // If an event is bound outside the Shadow DOM, the `event.target` will\n  // point to the shadow root so we have to use `composedPath` instead.\n  return event.composedPath ? event.composedPath()[0] : event.target;\n}\nexport { _getEventTarget as _, _getShadowRoot as a, _getFocusedElementPierceShadowDom as b, _supportsShadowDom as c };\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, <PERSON><PERSON><PERSON>, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output } from '@angular/core';\nimport { BehaviorSubject, Subject, of } from 'rxjs';\nimport { skip, distinctUntilChanged, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-84590b88.mjs';\nimport { A as ALT, C as CONTROL, M as MAC_META, a as META, S as SHIFT } from './keycodes-0e4398c6.mjs';\nimport { _ as _getEventTarget, a as _getShadowRoot } from './shadow-dom-318658ae.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-08253a84.mjs';\nimport { P as Platform } from './platform-20fc4de8.mjs';\nimport { n as normalizePassiveListenerOptions } from './passive-listeners-93cf8be8.mjs';\nimport { c as coerceElement } from './element-15999318.mjs';\n\n/**\n * Injectable options for the InputModalityDetector. These are shallowly merged with the default\n * options.\n */\nconst INPUT_MODALITY_DETECTOR_OPTIONS = new InjectionToken('cdk-input-modality-detector-options');\n/**\n * Default options for the InputModalityDetector.\n *\n * Modifier keys are ignored by default (i.e. when pressed won't cause the service to detect\n * keyboard input modality) for two reasons:\n *\n * 1. Modifier keys are commonly used with mouse to perform actions such as 'right click' or 'open\n *    in new tab', and are thus less representative of actual keyboard interaction.\n * 2. VoiceOver triggers some keyboard events when linearly navigating with Control + Option (but\n *    confusingly not with Caps Lock). Thus, to have parity with other screen readers, we ignore\n *    these keys so as to not update the input modality.\n *\n * Note that we do not by default ignore the right Meta key on Safari because it has the same key\n * code as the ContextMenu key on other browsers. When we switch to using event.key, we can\n * distinguish between the two.\n */\nconst INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS = {\n  ignoreKeys: [ALT, CONTROL, MAC_META, META, SHIFT]\n};\n/**\n * The amount of time needed to pass after a touchstart event in order for a subsequent mousedown\n * event to be attributed as mouse and not touch.\n *\n * This is the value used by AngularJS Material. Through trial and error (on iPhone 6S) they found\n * that a value of around 650ms seems appropriate.\n */\nconst TOUCH_BUFFER_MS = 650;\n/**\n * Event listener options that enable capturing and also mark the listener as passive if the browser\n * supports it.\n */\nconst modalityEventListenerOptions = {\n  passive: true,\n  capture: true\n};\n/**\n * Service that detects the user's input modality.\n *\n * This service does not update the input modality when a user navigates with a screen reader\n * (e.g. linear navigation with VoiceOver, object navigation / browse mode with NVDA, virtual PC\n * cursor mode with JAWS). This is in part due to technical limitations (i.e. keyboard events do not\n * fire as expected in these modes) but is also arguably the correct behavior. Navigating with a\n * screen reader is akin to visually scanning a page, and should not be interpreted as actual user\n * input interaction.\n *\n * When a user is not navigating but *interacting* with a screen reader, this service attempts to\n * update the input modality to keyboard, but in general this service's behavior is largely\n * undefined.\n */\nclass InputModalityDetector {\n  _platform = inject(Platform);\n  _listenerCleanups;\n  /** Emits whenever an input modality is detected. */\n  modalityDetected;\n  /** Emits when the input modality changes. */\n  modalityChanged;\n  /** The most recently detected input modality. */\n  get mostRecentModality() {\n    return this._modality.value;\n  }\n  /**\n   * The most recently detected input modality event target. Is null if no input modality has been\n   * detected or if the associated event target is null for some unknown reason.\n   */\n  _mostRecentTarget = null;\n  /** The underlying BehaviorSubject that emits whenever an input modality is detected. */\n  _modality = new BehaviorSubject(null);\n  /** Options for this InputModalityDetector. */\n  _options;\n  /**\n   * The timestamp of the last touch input modality. Used to determine whether mousedown events\n   * should be attributed to mouse or touch.\n   */\n  _lastTouchMs = 0;\n  /**\n   * Handles keydown events. Must be an arrow function in order to preserve the context when it gets\n   * bound.\n   */\n  _onKeydown = event => {\n    // If this is one of the keys we should ignore, then ignore it and don't update the input\n    // modality to keyboard.\n    if (this._options?.ignoreKeys?.some(keyCode => keyCode === event.keyCode)) {\n      return;\n    }\n    this._modality.next('keyboard');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  /**\n   * Handles mousedown events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  _onMousedown = event => {\n    // Touches trigger both touch and mouse events, so we need to distinguish between mouse events\n    // that were triggered via mouse vs touch. To do so, check if the mouse event occurs closely\n    // after the previous touch event.\n    if (Date.now() - this._lastTouchMs < TOUCH_BUFFER_MS) {\n      return;\n    }\n    // Fake mousedown events are fired by some screen readers when controls are activated by the\n    // screen reader. Attribute them to keyboard input modality.\n    this._modality.next(isFakeMousedownFromScreenReader(event) ? 'keyboard' : 'mouse');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  /**\n   * Handles touchstart events. Must be an arrow function in order to preserve the context when it\n   * gets bound.\n   */\n  _onTouchstart = event => {\n    // Same scenario as mentioned in _onMousedown, but on touch screen devices, fake touchstart\n    // events are fired. Again, attribute to keyboard input modality.\n    if (isFakeTouchstartFromScreenReader(event)) {\n      this._modality.next('keyboard');\n      return;\n    }\n    // Store the timestamp of this touch event, as it's used to distinguish between mouse events\n    // triggered via mouse vs touch.\n    this._lastTouchMs = Date.now();\n    this._modality.next('touch');\n    this._mostRecentTarget = _getEventTarget(event);\n  };\n  constructor() {\n    const ngZone = inject(NgZone);\n    const document = inject(DOCUMENT);\n    const options = inject(INPUT_MODALITY_DETECTOR_OPTIONS, {\n      optional: true\n    });\n    this._options = {\n      ...INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS,\n      ...options\n    };\n    // Skip the first emission as it's null.\n    this.modalityDetected = this._modality.pipe(skip(1));\n    this.modalityChanged = this.modalityDetected.pipe(distinctUntilChanged());\n    // If we're not in a browser, this service should do nothing, as there's no relevant input\n    // modality to detect.\n    if (this._platform.isBrowser) {\n      const renderer = inject(RendererFactory2).createRenderer(null, null);\n      this._listenerCleanups = ngZone.runOutsideAngular(() => {\n        return [_bindEventWithOptions(renderer, document, 'keydown', this._onKeydown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'mousedown', this._onMousedown, modalityEventListenerOptions), _bindEventWithOptions(renderer, document, 'touchstart', this._onTouchstart, modalityEventListenerOptions)];\n      });\n    }\n  }\n  ngOnDestroy() {\n    this._modality.complete();\n    this._listenerCleanups?.forEach(cleanup => cleanup());\n  }\n  static ɵfac = function InputModalityDetector_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InputModalityDetector)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InputModalityDetector,\n    factory: InputModalityDetector.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputModalityDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Detection mode used for attributing the origin of a focus event. */\nvar FocusMonitorDetectionMode;\n(function (FocusMonitorDetectionMode) {\n  /**\n   * Any mousedown, keydown, or touchstart event that happened in the previous\n   * tick or the current tick will be used to assign a focus event's origin (to\n   * either mouse, keyboard, or touch). This is the default option.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"IMMEDIATE\"] = 0] = \"IMMEDIATE\";\n  /**\n   * A focus event's origin is always attributed to the last corresponding\n   * mousedown, keydown, or touchstart event, no matter how long ago it occurred.\n   */\n  FocusMonitorDetectionMode[FocusMonitorDetectionMode[\"EVENTUAL\"] = 1] = \"EVENTUAL\";\n})(FocusMonitorDetectionMode || (FocusMonitorDetectionMode = {}));\n/** InjectionToken for FocusMonitorOptions. */\nconst FOCUS_MONITOR_DEFAULT_OPTIONS = new InjectionToken('cdk-focus-monitor-default-options');\n/**\n * Event listener options that enable capturing and also\n * mark the listener as passive if the browser supports it.\n */\nconst captureEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true,\n  capture: true\n});\n/** Monitors mouse and keyboard events to determine the cause of focus events. */\nclass FocusMonitor {\n  _ngZone = inject(NgZone);\n  _platform = inject(Platform);\n  _inputModalityDetector = inject(InputModalityDetector);\n  /** The focus origin that the next focus event is a result of. */\n  _origin = null;\n  /** The FocusOrigin of the last focus event tracked by the FocusMonitor. */\n  _lastFocusOrigin;\n  /** Whether the window has just been focused. */\n  _windowFocused = false;\n  /** The timeout id of the window focus timeout. */\n  _windowFocusTimeoutId;\n  /** The timeout id of the origin clearing timeout. */\n  _originTimeoutId;\n  /**\n   * Whether the origin was determined via a touch interaction. Necessary as properly attributing\n   * focus events to touch interactions requires special logic.\n   */\n  _originFromTouchInteraction = false;\n  /** Map of elements being monitored to their info. */\n  _elementInfo = new Map();\n  /** The number of elements currently being monitored. */\n  _monitoredElementCount = 0;\n  /**\n   * Keeps track of the root nodes to which we've currently bound a focus/blur handler,\n   * as well as the number of monitored elements that they contain. We have to treat focus/blur\n   * handlers differently from the rest of the events, because the browser won't emit events\n   * to the document when focus moves inside of a shadow root.\n   */\n  _rootNodeFocusListenerCount = new Map();\n  /**\n   * The specified detection mode, used for attributing the origin of a focus\n   * event.\n   */\n  _detectionMode;\n  /**\n   * Event listener for `focus` events on the window.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  _windowFocusListener = () => {\n    // Make a note of when the window regains focus, so we can\n    // restore the origin info for the focused element.\n    this._windowFocused = true;\n    this._windowFocusTimeoutId = setTimeout(() => this._windowFocused = false);\n  };\n  /** Used to reference correct document/window */\n  _document = inject(DOCUMENT, {\n    optional: true\n  });\n  /** Subject for stopping our InputModalityDetector subscription. */\n  _stopInputModalityDetector = new Subject();\n  constructor() {\n    const options = inject(FOCUS_MONITOR_DEFAULT_OPTIONS, {\n      optional: true\n    });\n    this._detectionMode = options?.detectionMode || FocusMonitorDetectionMode.IMMEDIATE;\n  }\n  /**\n   * Event listener for `focus` and 'blur' events on the document.\n   * Needs to be an arrow function in order to preserve the context when it gets bound.\n   */\n  _rootNodeFocusAndBlurListener = event => {\n    const target = _getEventTarget(event);\n    // We need to walk up the ancestor chain in order to support `checkChildren`.\n    for (let element = target; element; element = element.parentElement) {\n      if (event.type === 'focus') {\n        this._onFocus(event, element);\n      } else {\n        this._onBlur(event, element);\n      }\n    }\n  };\n  monitor(element, checkChildren = false) {\n    const nativeElement = coerceElement(element);\n    // Do nothing if we're not on the browser platform or the passed in node isn't an element.\n    if (!this._platform.isBrowser || nativeElement.nodeType !== 1) {\n      // Note: we don't want the observable to emit at all so we don't pass any parameters.\n      return of();\n    }\n    // If the element is inside the shadow DOM, we need to bind our focus/blur listeners to\n    // the shadow root, rather than the `document`, because the browser won't emit focus events\n    // to the `document`, if focus is moving within the same shadow root.\n    const rootNode = _getShadowRoot(nativeElement) || this._getDocument();\n    const cachedInfo = this._elementInfo.get(nativeElement);\n    // Check if we're already monitoring this element.\n    if (cachedInfo) {\n      if (checkChildren) {\n        // TODO(COMP-318): this can be problematic, because it'll turn all non-checkChildren\n        // observers into ones that behave as if `checkChildren` was turned on. We need a more\n        // robust solution.\n        cachedInfo.checkChildren = true;\n      }\n      return cachedInfo.subject;\n    }\n    // Create monitored element info.\n    const info = {\n      checkChildren: checkChildren,\n      subject: new Subject(),\n      rootNode\n    };\n    this._elementInfo.set(nativeElement, info);\n    this._registerGlobalListeners(info);\n    return info.subject;\n  }\n  stopMonitoring(element) {\n    const nativeElement = coerceElement(element);\n    const elementInfo = this._elementInfo.get(nativeElement);\n    if (elementInfo) {\n      elementInfo.subject.complete();\n      this._setClasses(nativeElement);\n      this._elementInfo.delete(nativeElement);\n      this._removeGlobalListeners(elementInfo);\n    }\n  }\n  focusVia(element, origin, options) {\n    const nativeElement = coerceElement(element);\n    const focusedElement = this._getDocument().activeElement;\n    // If the element is focused already, calling `focus` again won't trigger the event listener\n    // which means that the focus classes won't be updated. If that's the case, update the classes\n    // directly without waiting for an event.\n    if (nativeElement === focusedElement) {\n      this._getClosestElementsInfo(nativeElement).forEach(([currentElement, info]) => this._originChanged(currentElement, origin, info));\n    } else {\n      this._setOrigin(origin);\n      // `focus` isn't available on the server\n      if (typeof nativeElement.focus === 'function') {\n        nativeElement.focus(options);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._elementInfo.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  /** Access injected document if available or fallback to global document reference */\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n  _getFocusOrigin(focusEventTarget) {\n    if (this._origin) {\n      // If the origin was realized via a touch interaction, we need to perform additional checks\n      // to determine whether the focus origin should be attributed to touch or program.\n      if (this._originFromTouchInteraction) {\n        return this._shouldBeAttributedToTouch(focusEventTarget) ? 'touch' : 'program';\n      } else {\n        return this._origin;\n      }\n    }\n    // If the window has just regained focus, we can restore the most recent origin from before the\n    // window blurred. Otherwise, we've reached the point where we can't identify the source of the\n    // focus. This typically means one of two things happened:\n    //\n    // 1) The element was programmatically focused, or\n    // 2) The element was focused via screen reader navigation (which generally doesn't fire\n    //    events).\n    //\n    // Because we can't distinguish between these two cases, we default to setting `program`.\n    if (this._windowFocused && this._lastFocusOrigin) {\n      return this._lastFocusOrigin;\n    }\n    // If the interaction is coming from an input label, we consider it a mouse interactions.\n    // This is a special case where focus moves on `click`, rather than `mousedown` which breaks\n    // our detection, because all our assumptions are for `mousedown`. We need to handle this\n    // special case, because it's very common for checkboxes and radio buttons.\n    if (focusEventTarget && this._isLastInteractionFromInputLabel(focusEventTarget)) {\n      return 'mouse';\n    }\n    return 'program';\n  }\n  /**\n   * Returns whether the focus event should be attributed to touch. Recall that in IMMEDIATE mode, a\n   * touch origin isn't immediately reset at the next tick (see _setOrigin). This means that when we\n   * handle a focus event following a touch interaction, we need to determine whether (1) the focus\n   * event was directly caused by the touch interaction or (2) the focus event was caused by a\n   * subsequent programmatic focus call triggered by the touch interaction.\n   * @param focusEventTarget The target of the focus event under examination.\n   */\n  _shouldBeAttributedToTouch(focusEventTarget) {\n    // Please note that this check is not perfect. Consider the following edge case:\n    //\n    // <div #parent tabindex=\"0\">\n    //   <div #child tabindex=\"0\" (click)=\"#parent.focus()\"></div>\n    // </div>\n    //\n    // Suppose there is a FocusMonitor in IMMEDIATE mode attached to #parent. When the user touches\n    // #child, #parent is programmatically focused. This code will attribute the focus to touch\n    // instead of program. This is a relatively minor edge-case that can be worked around by using\n    // focusVia(parent, 'program') to focus #parent.\n    return this._detectionMode === FocusMonitorDetectionMode.EVENTUAL || !!focusEventTarget?.contains(this._inputModalityDetector._mostRecentTarget);\n  }\n  /**\n   * Sets the focus classes on the element based on the given focus origin.\n   * @param element The element to update the classes on.\n   * @param origin The focus origin.\n   */\n  _setClasses(element, origin) {\n    element.classList.toggle('cdk-focused', !!origin);\n    element.classList.toggle('cdk-touch-focused', origin === 'touch');\n    element.classList.toggle('cdk-keyboard-focused', origin === 'keyboard');\n    element.classList.toggle('cdk-mouse-focused', origin === 'mouse');\n    element.classList.toggle('cdk-program-focused', origin === 'program');\n  }\n  /**\n   * Updates the focus origin. If we're using immediate detection mode, we schedule an async\n   * function to clear the origin at the end of a timeout. The duration of the timeout depends on\n   * the origin being set.\n   * @param origin The origin to set.\n   * @param isFromInteraction Whether we are setting the origin from an interaction event.\n   */\n  _setOrigin(origin, isFromInteraction = false) {\n    this._ngZone.runOutsideAngular(() => {\n      this._origin = origin;\n      this._originFromTouchInteraction = origin === 'touch' && isFromInteraction;\n      // If we're in IMMEDIATE mode, reset the origin at the next tick (or in `TOUCH_BUFFER_MS` ms\n      // for a touch event). We reset the origin at the next tick because Firefox focuses one tick\n      // after the interaction event. We wait `TOUCH_BUFFER_MS` ms before resetting the origin for\n      // a touch event because when a touch event is fired, the associated focus event isn't yet in\n      // the event queue. Before doing so, clear any pending timeouts.\n      if (this._detectionMode === FocusMonitorDetectionMode.IMMEDIATE) {\n        clearTimeout(this._originTimeoutId);\n        const ms = this._originFromTouchInteraction ? TOUCH_BUFFER_MS : 1;\n        this._originTimeoutId = setTimeout(() => this._origin = null, ms);\n      }\n    });\n  }\n  /**\n   * Handles focus events on a registered element.\n   * @param event The focus event.\n   * @param element The monitored element.\n   */\n  _onFocus(event, element) {\n    // NOTE(mmalerba): We currently set the classes based on the focus origin of the most recent\n    // focus event affecting the monitored element. If we want to use the origin of the first event\n    // instead we should check for the cdk-focused class here and return if the element already has\n    // it. (This only matters for elements that have includesChildren = true).\n    // If we are not counting child-element-focus as focused, make sure that the event target is the\n    // monitored element itself.\n    const elementInfo = this._elementInfo.get(element);\n    const focusEventTarget = _getEventTarget(event);\n    if (!elementInfo || !elementInfo.checkChildren && element !== focusEventTarget) {\n      return;\n    }\n    this._originChanged(element, this._getFocusOrigin(focusEventTarget), elementInfo);\n  }\n  /**\n   * Handles blur events on a registered element.\n   * @param event The blur event.\n   * @param element The monitored element.\n   */\n  _onBlur(event, element) {\n    // If we are counting child-element-focus as focused, make sure that we aren't just blurring in\n    // order to focus another child of the monitored element.\n    const elementInfo = this._elementInfo.get(element);\n    if (!elementInfo || elementInfo.checkChildren && event.relatedTarget instanceof Node && element.contains(event.relatedTarget)) {\n      return;\n    }\n    this._setClasses(element);\n    this._emitOrigin(elementInfo, null);\n  }\n  _emitOrigin(info, origin) {\n    if (info.subject.observers.length) {\n      this._ngZone.run(() => info.subject.next(origin));\n    }\n  }\n  _registerGlobalListeners(elementInfo) {\n    if (!this._platform.isBrowser) {\n      return;\n    }\n    const rootNode = elementInfo.rootNode;\n    const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode) || 0;\n    if (!rootNodeFocusListeners) {\n      this._ngZone.runOutsideAngular(() => {\n        rootNode.addEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.addEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n      });\n    }\n    this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners + 1);\n    // Register global listeners when first element is monitored.\n    if (++this._monitoredElementCount === 1) {\n      // Note: we listen to events in the capture phase so we\n      // can detect them even if the user stops propagation.\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        window.addEventListener('focus', this._windowFocusListener);\n      });\n      // The InputModalityDetector is also just a collection of global listeners.\n      this._inputModalityDetector.modalityDetected.pipe(takeUntil(this._stopInputModalityDetector)).subscribe(modality => {\n        this._setOrigin(modality, true /* isFromInteraction */);\n      });\n    }\n  }\n  _removeGlobalListeners(elementInfo) {\n    const rootNode = elementInfo.rootNode;\n    if (this._rootNodeFocusListenerCount.has(rootNode)) {\n      const rootNodeFocusListeners = this._rootNodeFocusListenerCount.get(rootNode);\n      if (rootNodeFocusListeners > 1) {\n        this._rootNodeFocusListenerCount.set(rootNode, rootNodeFocusListeners - 1);\n      } else {\n        rootNode.removeEventListener('focus', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        rootNode.removeEventListener('blur', this._rootNodeFocusAndBlurListener, captureEventListenerOptions);\n        this._rootNodeFocusListenerCount.delete(rootNode);\n      }\n    }\n    // Unregister global listeners when last element is unmonitored.\n    if (! --this._monitoredElementCount) {\n      const window = this._getWindow();\n      window.removeEventListener('focus', this._windowFocusListener);\n      // Equivalently, stop our InputModalityDetector subscription.\n      this._stopInputModalityDetector.next();\n      // Clear timeouts for all potentially pending timeouts to prevent the leaks.\n      clearTimeout(this._windowFocusTimeoutId);\n      clearTimeout(this._originTimeoutId);\n    }\n  }\n  /** Updates all the state on an element once its focus origin has changed. */\n  _originChanged(element, origin, elementInfo) {\n    this._setClasses(element, origin);\n    this._emitOrigin(elementInfo, origin);\n    this._lastFocusOrigin = origin;\n  }\n  /**\n   * Collects the `MonitoredElementInfo` of a particular element and\n   * all of its ancestors that have enabled `checkChildren`.\n   * @param element Element from which to start the search.\n   */\n  _getClosestElementsInfo(element) {\n    const results = [];\n    this._elementInfo.forEach((info, currentElement) => {\n      if (currentElement === element || info.checkChildren && currentElement.contains(element)) {\n        results.push([currentElement, info]);\n      }\n    });\n    return results;\n  }\n  /**\n   * Returns whether an interaction is likely to have come from the user clicking the `label` of\n   * an `input` or `textarea` in order to focus it.\n   * @param focusEventTarget Target currently receiving focus.\n   */\n  _isLastInteractionFromInputLabel(focusEventTarget) {\n    const {\n      _mostRecentTarget: mostRecentTarget,\n      mostRecentModality\n    } = this._inputModalityDetector;\n    // If the last interaction used the mouse on an element contained by one of the labels\n    // of an `input`/`textarea` that is currently focused, it is very likely that the\n    // user redirected focus using the label.\n    if (mostRecentModality !== 'mouse' || !mostRecentTarget || mostRecentTarget === focusEventTarget || focusEventTarget.nodeName !== 'INPUT' && focusEventTarget.nodeName !== 'TEXTAREA' || focusEventTarget.disabled) {\n      return false;\n    }\n    const labels = focusEventTarget.labels;\n    if (labels) {\n      for (let i = 0; i < labels.length; i++) {\n        if (labels[i].contains(mostRecentTarget)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  static ɵfac = function FocusMonitor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusMonitor)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusMonitor,\n    factory: FocusMonitor.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive that determines how a particular element was focused (via keyboard, mouse, touch, or\n * programmatically) and adds corresponding classes to the element.\n *\n * There are two variants of this directive:\n * 1) cdkMonitorElementFocus: does not consider an element to be focused if one of its children is\n *    focused.\n * 2) cdkMonitorSubtreeFocus: considers an element focused if it or any of its children are focused.\n */\nclass CdkMonitorFocus {\n  _elementRef = inject(ElementRef);\n  _focusMonitor = inject(FocusMonitor);\n  _monitorSubscription;\n  _focusOrigin = null;\n  cdkFocusChange = new EventEmitter();\n  constructor() {}\n  get focusOrigin() {\n    return this._focusOrigin;\n  }\n  ngAfterViewInit() {\n    const element = this._elementRef.nativeElement;\n    this._monitorSubscription = this._focusMonitor.monitor(element, element.nodeType === 1 && element.hasAttribute('cdkMonitorSubtreeFocus')).subscribe(origin => {\n      this._focusOrigin = origin;\n      this.cdkFocusChange.emit(origin);\n    });\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    if (this._monitorSubscription) {\n      this._monitorSubscription.unsubscribe();\n    }\n  }\n  static ɵfac = function CdkMonitorFocus_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkMonitorFocus)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkMonitorFocus,\n    selectors: [[\"\", \"cdkMonitorElementFocus\", \"\"], [\"\", \"cdkMonitorSubtreeFocus\", \"\"]],\n    outputs: {\n      cdkFocusChange: \"cdkFocusChange\"\n    },\n    exportAs: [\"cdkMonitorFocus\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkMonitorFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkMonitorElementFocus], [cdkMonitorSubtreeFocus]',\n      exportAs: 'cdkMonitorFocus'\n    }]\n  }], () => [], {\n    cdkFocusChange: [{\n      type: Output\n    }]\n  });\n})();\nexport { CdkMonitorFocus as C, FocusMonitorDetectionMode as F, InputModalityDetector as I, INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS as a, INPUT_MODALITY_DETECTOR_OPTIONS as b, FOCUS_MONITOR_DEFAULT_OPTIONS as c, FocusMonitor as d };\n", "/** Gets whether an event could be a faked `mousedown` event dispatched by a screen reader. */\nfunction isFakeMousedownFromScreenReader(event) {\n  // Some screen readers will dispatch a fake `mousedown` event when pressing enter or space on\n  // a clickable element. We can distinguish these events when `event.buttons` is zero, or\n  // `event.detail` is zero depending on the browser:\n  // - `event.buttons` works on Firefox, but fails on Chrome.\n  // - `detail` works on Chrome, but fails on Firefox.\n  return event.buttons === 0 || event.detail === 0;\n}\n/** Gets whether an event could be a faked `touchstart` event dispatched by a screen reader. */\nfunction isFakeTouchstartFromScreenReader(event) {\n  const touch = event.touches && event.touches[0] || event.changedTouches && event.changedTouches[0];\n  // A fake `touchstart` can be distinguished from a real one by looking at the `identifier`\n  // which is typically >= 0 on a real device versus -1 from a screen reader. Just to be safe,\n  // we can also look at `radiusX` and `radiusY`. This behavior was observed against a Windows 10\n  // device with a touch screen running NVDA v2020.4 and Firefox 85 or Chrome 88.\n  return !!touch && touch.identifier === -1 && (touch.radiusX == null || touch.radiusX === 1) && (touch.radiusY == null || touch.radiusY === 1);\n}\nexport { isFakeTouchstartFromScreenReader as a, isFakeMousedownFromScreenReader as i };\n", "/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents;\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nfunction supportsPassiveEventListeners() {\n  if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n    try {\n      window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n        get: () => supportsPassiveEvents = true\n      }));\n    } finally {\n      supportsPassiveEvents = supportsPassiveEvents || false;\n    }\n  }\n  return supportsPassiveEvents;\n}\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\nfunction normalizePassiveListenerOptions(options) {\n  return supportsPassiveEventListeners() ? options : !!options.capture;\n}\nexport { normalizePassiveListenerOptions as n, supportsPassiveEventListeners as s };\n", "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load the .cdk-visually-hidden styles.\n * @docs-private\n */\nclass _VisuallyHiddenLoader {\n  static ɵfac = function _VisuallyHiddenLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _VisuallyHiddenLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _VisuallyHiddenLoader,\n    selectors: [[\"ng-component\"]],\n    exportAs: [\"cdkVisuallyHidden\"],\n    decls: 0,\n    vars: 0,\n    template: function _VisuallyHiddenLoader_Template(rf, ctx) {},\n    styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_VisuallyHiddenLoader, [{\n    type: Component,\n    args: [{\n      exportAs: 'cdkVisuallyHidden',\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}\"]\n    }]\n  }], null, null);\n})();\nexport { _VisuallyHiddenLoader as _ };\n", "import * as i0 from '@angular/core';\nimport { inject, CSP_NONCE, Injectable, NgZone } from '@angular/core';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport { P as Platform } from './platform-20fc4de8.mjs';\nimport { c as coerceArray } from './array-6239d2f8.mjs';\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n  _platform = inject(Platform);\n  _nonce = inject(CSP_NONCE, {\n    optional: true\n  });\n  /** The internal matchMedia method to return back a MediaQueryList like object. */\n  _matchMedia;\n  constructor() {\n    this._matchMedia = this._platform.isBrowser && window.matchMedia ?\n    // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n    // call it from a different scope.\n    window.matchMedia.bind(window) : noopMatchMedia;\n  }\n  /**\n   * Evaluates the given media query and returns the native MediaQueryList from which results\n   * can be retrieved.\n   * Confirms the layout engine will trigger for the selector query provided and returns the\n   * MediaQueryList for the query provided.\n   */\n  matchMedia(query) {\n    if (this._platform.WEBKIT || this._platform.BLINK) {\n      createEmptyStyleRule(query, this._nonce);\n    }\n    return this._matchMedia(query);\n  }\n  static ɵfac = function MediaMatcher_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MediaMatcher)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MediaMatcher,\n    factory: MediaMatcher.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MediaMatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n  if (mediaQueriesForWebkitCompatibility.has(query)) {\n    return;\n  }\n  try {\n    if (!mediaQueryStyleNode) {\n      mediaQueryStyleNode = document.createElement('style');\n      if (nonce) {\n        mediaQueryStyleNode.setAttribute('nonce', nonce);\n      }\n      mediaQueryStyleNode.setAttribute('type', 'text/css');\n      document.head.appendChild(mediaQueryStyleNode);\n    }\n    if (mediaQueryStyleNode.sheet) {\n      mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n      mediaQueriesForWebkitCompatibility.add(query);\n    }\n  } catch (e) {\n    console.error(e);\n  }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n  // Use `as any` here to avoid adding additional necessary properties for\n  // the noop matcher.\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {}\n  };\n}\n\n/** Utility for checking the matching state of `@media` queries. */\nclass BreakpointObserver {\n  _mediaMatcher = inject(MediaMatcher);\n  _zone = inject(NgZone);\n  /**  A map of all media queries currently being listened for. */\n  _queries = new Map();\n  /** A subject for all other observables to takeUntil based on. */\n  _destroySubject = new Subject();\n  constructor() {}\n  /** Completes the active subject, signalling to all other observables to complete. */\n  ngOnDestroy() {\n    this._destroySubject.next();\n    this._destroySubject.complete();\n  }\n  /**\n   * Whether one or more media queries match the current viewport size.\n   * @param value One or more media queries to check.\n   * @returns Whether any of the media queries match.\n   */\n  isMatched(value) {\n    const queries = splitQueries(coerceArray(value));\n    return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n  }\n  /**\n   * Gets an observable of results for the given queries that will emit new results for any changes\n   * in matching of the given queries.\n   * @param value One or more media queries to check.\n   * @returns A stream of matches for the given queries.\n   */\n  observe(value) {\n    const queries = splitQueries(coerceArray(value));\n    const observables = queries.map(query => this._registerQuery(query).observable);\n    let stateObservable = combineLatest(observables);\n    // Emit the first state immediately, and then debounce the subsequent emissions.\n    stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n    return stateObservable.pipe(map(breakpointStates => {\n      const response = {\n        matches: false,\n        breakpoints: {}\n      };\n      breakpointStates.forEach(({\n        matches,\n        query\n      }) => {\n        response.matches = response.matches || matches;\n        response.breakpoints[query] = matches;\n      });\n      return response;\n    }));\n  }\n  /** Registers a specific query to be listened for. */\n  _registerQuery(query) {\n    // Only set up a new MediaQueryList if it is not already being listened for.\n    if (this._queries.has(query)) {\n      return this._queries.get(query);\n    }\n    const mql = this._mediaMatcher.matchMedia(query);\n    // Create callback for match changes and add it is as a listener.\n    const queryObservable = new Observable(observer => {\n      // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n      // back into the zone because matchMedia is only included in Zone.js by loading the\n      // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n      // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n      // patches it.\n      const handler = e => this._zone.run(() => observer.next(e));\n      mql.addListener(handler);\n      return () => {\n        mql.removeListener(handler);\n      };\n    }).pipe(startWith(mql), map(({\n      matches\n    }) => ({\n      query,\n      matches\n    })), takeUntil(this._destroySubject));\n    // Add the MediaQueryList to the set of queries.\n    const output = {\n      observable: queryObservable,\n      mql\n    };\n    this._queries.set(query, output);\n    return output;\n  }\n  static ɵfac = function BreakpointObserver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BreakpointObserver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: BreakpointObserver,\n    factory: BreakpointObserver.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreakpointObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n  return queries.map(query => query.split(',')).reduce((a1, a2) => a1.concat(a2)).map(query => query.trim());\n}\nexport { BreakpointObserver as B, MediaMatcher as M };\n", "function coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\nexport { coerceArray as c };\n", "import * as i0 from '@angular/core';\nimport { Injectable, inject, Ng<PERSON>one, ElementRef, EventEmitter, booleanAttribute, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { map, filter, debounceTime } from 'rxjs/operators';\nimport { a as coerceNumberProperty, c as coerceElement } from './element-15999318.mjs';\n\n// <PERSON><PERSON> may add, remove, or edit comment nodes during change detection. We don't care about\n// these changes because they don't affect the user-preceived content, and worse it can cause\n// infinite change detection cycles where the change detection updates a comment, triggering the\n// MutationObserver, triggering another change detection and kicking the cycle off again.\nfunction shouldIgnoreRecord(record) {\n  // Ignore changes to comment text.\n  if (record.type === 'characterData' && record.target instanceof Comment) {\n    return true;\n  }\n  // Ignore addition / removal of comments.\n  if (record.type === 'childList') {\n    for (let i = 0; i < record.addedNodes.length; i++) {\n      if (!(record.addedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    for (let i = 0; i < record.removedNodes.length; i++) {\n      if (!(record.removedNodes[i] instanceof Comment)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  // Observe everything else.\n  return false;\n}\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n  create(callback) {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n  static ɵfac = function MutationObserverFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MutationObserverFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MutationObserverFactory,\n    factory: MutationObserverFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MutationObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n  _mutationObserverFactory = inject(MutationObserverFactory);\n  /** Keeps track of the existing MutationObservers so they can be reused. */\n  _observedElements = new Map();\n  _ngZone = inject(NgZone);\n  constructor() {}\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this._observeElement(element);\n      const subscription = stream.pipe(map(records => records.filter(record => !shouldIgnoreRecord(record))), filter(records => !!records.length)).subscribe(records => {\n        this._ngZone.run(() => {\n          observer.next(records);\n        });\n      });\n      return () => {\n        subscription.unsubscribe();\n        this._unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n  _observeElement(element) {\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._observedElements.has(element)) {\n        const stream = new Subject();\n        const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n        if (observer) {\n          observer.observe(element, {\n            characterData: true,\n            childList: true,\n            subtree: true\n          });\n        }\n        this._observedElements.set(element, {\n          observer,\n          stream,\n          count: 1\n        });\n      } else {\n        this._observedElements.get(element).count++;\n      }\n      return this._observedElements.get(element).stream;\n    });\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n  _unobserveElement(element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element).count--;\n      if (!this._observedElements.get(element).count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying MutationObserver for the specified element. */\n  _cleanupObserver(element) {\n    if (this._observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this._observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this._observedElements.delete(element);\n    }\n  }\n  static ɵfac = function ContentObserver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ContentObserver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ContentObserver,\n    factory: ContentObserver.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n  _contentObserver = inject(ContentObserver);\n  _elementRef = inject(ElementRef);\n  /** Event emitted for each change in the element's content. */\n  event = new EventEmitter();\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  _disabled = false;\n  /** Debounce interval for emitting the changes. */\n  get debounce() {\n    return this._debounce;\n  }\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n    this._subscribe();\n  }\n  _debounce;\n  _currentSubscription = null;\n  constructor() {}\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n  _subscribe() {\n    this._unsubscribe();\n    const stream = this._contentObserver.observe(this._elementRef);\n    this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n  }\n  _unsubscribe() {\n    this._currentSubscription?.unsubscribe();\n  }\n  static ɵfac = function CdkObserveContent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkObserveContent)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkObserveContent,\n    selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n    inputs: {\n      disabled: [2, \"cdkObserveContentDisabled\", \"disabled\", booleanAttribute],\n      debounce: \"debounce\"\n    },\n    outputs: {\n      event: \"cdkObserveContent\"\n    },\n    exportAs: [\"cdkObserveContent\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkObserveContent, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkObserveContent]',\n      exportAs: 'cdkObserveContent'\n    }]\n  }], () => [], {\n    event: [{\n      type: Output,\n      args: ['cdkObserveContent']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkObserveContentDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    debounce: [{\n      type: Input\n    }]\n  });\n})();\nclass ObserversModule {\n  static ɵfac = function ObserversModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ObserversModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ObserversModule,\n    imports: [CdkObserveContent],\n    exports: [CdkObserveContent]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MutationObserverFactory]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ObserversModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkObserveContent],\n      exports: [CdkObserveContent],\n      providers: [MutationObserverFactory]\n    }]\n  }], null, null);\n})();\nexport { ContentObserver as C, MutationObserverFactory as M, ObserversModule as O, CdkObserveContent as a };\n", "import * as i0 from '@angular/core';\nimport { inject, Injectable, afterNextRender, NgZone, Injector, ElementRef, booleanAttribute, Directive, Input, InjectionToken, NgModule } from '@angular/core';\nimport { C as CdkMonitorFocus } from './focus-monitor-28b6c826.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { P as Platform } from './platform-20fc4de8.mjs';\nimport { b as _getFocusedElementPierceShadowDom } from './shadow-dom-318658ae.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-09eecacc.mjs';\nimport { _ as _VisuallyHiddenLoader } from './visually-hidden-9a93b8bb.mjs';\nimport { B as BreakpointObserver } from './breakpoints-observer-8e7409df.mjs';\nimport { C as ContentObserver, O as ObserversModule } from './observe-content-937cdfbe.mjs';\n\n/**\n * Configuration for the isFocusable method.\n */\nclass IsFocusableConfig {\n  /**\n   * Whether to count an element as focusable even if it is not currently visible.\n   */\n  ignoreVisibility = false;\n}\n// The InteractivityChecker leans heavily on the ally.js accessibility utilities.\n// Methods like `isTabbable` are only covering specific edge-cases for the browsers which are\n// supported.\n/**\n * Utility for checking the interactivity of an element, such as whether it is focusable or\n * tabbable.\n */\nclass InteractivityChecker {\n  _platform = inject(Platform);\n  constructor() {}\n  /**\n   * Gets whether an element is disabled.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is disabled.\n   */\n  isDisabled(element) {\n    // This does not capture some cases, such as a non-form control with a disabled attribute or\n    // a form control inside of a disabled form, but should capture the most common cases.\n    return element.hasAttribute('disabled');\n  }\n  /**\n   * Gets whether an element is visible for the purposes of interactivity.\n   *\n   * This will capture states like `display: none` and `visibility: hidden`, but not things like\n   * being clipped by an `overflow: hidden` parent or being outside the viewport.\n   *\n   * @returns Whether the element is visible.\n   */\n  isVisible(element) {\n    return hasGeometry(element) && getComputedStyle(element).visibility === 'visible';\n  }\n  /**\n   * Gets whether an element can be reached via Tab key.\n   * Assumes that the element has already been checked with isFocusable.\n   *\n   * @param element Element to be checked.\n   * @returns Whether the element is tabbable.\n   */\n  isTabbable(element) {\n    // Nothing is tabbable on the server 😎\n    if (!this._platform.isBrowser) {\n      return false;\n    }\n    const frameElement = getFrameElement(getWindow(element));\n    if (frameElement) {\n      // Frame elements inherit their tabindex onto all child elements.\n      if (getTabIndexValue(frameElement) === -1) {\n        return false;\n      }\n      // Browsers disable tabbing to an element inside of an invisible frame.\n      if (!this.isVisible(frameElement)) {\n        return false;\n      }\n    }\n    let nodeName = element.nodeName.toLowerCase();\n    let tabIndexValue = getTabIndexValue(element);\n    if (element.hasAttribute('contenteditable')) {\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'iframe' || nodeName === 'object') {\n      // The frame or object's content may be tabbable depending on the content, but it's\n      // not possibly to reliably detect the content of the frames. We always consider such\n      // elements as non-tabbable.\n      return false;\n    }\n    // In iOS, the browser only considers some specific elements as tabbable.\n    if (this._platform.WEBKIT && this._platform.IOS && !isPotentiallyTabbableIOS(element)) {\n      return false;\n    }\n    if (nodeName === 'audio') {\n      // Audio elements without controls enabled are never tabbable, regardless\n      // of the tabindex attribute explicitly being set.\n      if (!element.hasAttribute('controls')) {\n        return false;\n      }\n      // Audio elements with controls are by default tabbable unless the\n      // tabindex attribute is set to `-1` explicitly.\n      return tabIndexValue !== -1;\n    }\n    if (nodeName === 'video') {\n      // For all video elements, if the tabindex attribute is set to `-1`, the video\n      // is not tabbable. Note: We cannot rely on the default `HTMLElement.tabIndex`\n      // property as that one is set to `-1` in Chrome, Edge and Safari v13.1. The\n      // tabindex attribute is the source of truth here.\n      if (tabIndexValue === -1) {\n        return false;\n      }\n      // If the tabindex is explicitly set, and not `-1` (as per check before), the\n      // video element is always tabbable (regardless of whether it has controls or not).\n      if (tabIndexValue !== null) {\n        return true;\n      }\n      // Otherwise (when no explicit tabindex is set), a video is only tabbable if it\n      // has controls enabled. Firefox is special as videos are always tabbable regardless\n      // of whether there are controls or not.\n      return this._platform.FIREFOX || element.hasAttribute('controls');\n    }\n    return element.tabIndex >= 0;\n  }\n  /**\n   * Gets whether an element can be focused by the user.\n   *\n   * @param element Element to be checked.\n   * @param config The config object with options to customize this method's behavior\n   * @returns Whether the element is focusable.\n   */\n  isFocusable(element, config) {\n    // Perform checks in order of left to most expensive.\n    // Again, naive approach that does not capture many edge cases and browser quirks.\n    return isPotentiallyFocusable(element) && !this.isDisabled(element) && (config?.ignoreVisibility || this.isVisible(element));\n  }\n  static ɵfac = function InteractivityChecker_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || InteractivityChecker)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: InteractivityChecker,\n    factory: InteractivityChecker.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InteractivityChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Returns the frame element from a window object. Since browsers like MS Edge throw errors if\n * the frameElement property is being accessed from a different host address, this property\n * should be accessed carefully.\n */\nfunction getFrameElement(window) {\n  try {\n    return window.frameElement;\n  } catch {\n    return null;\n  }\n}\n/** Checks whether the specified element has any geometry / rectangles. */\nfunction hasGeometry(element) {\n  // Use logic from jQuery to check for an invisible element.\n  // See https://github.com/jquery/jquery/blob/master/src/css/hiddenVisibleSelectors.js#L12\n  return !!(element.offsetWidth || element.offsetHeight || typeof element.getClientRects === 'function' && element.getClientRects().length);\n}\n/** Gets whether an element's  */\nfunction isNativeFormElement(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  return nodeName === 'input' || nodeName === 'select' || nodeName === 'button' || nodeName === 'textarea';\n}\n/** Gets whether an element is an `<input type=\"hidden\">`. */\nfunction isHiddenInput(element) {\n  return isInputElement(element) && element.type == 'hidden';\n}\n/** Gets whether an element is an anchor that has an href attribute. */\nfunction isAnchorWithHref(element) {\n  return isAnchorElement(element) && element.hasAttribute('href');\n}\n/** Gets whether an element is an input element. */\nfunction isInputElement(element) {\n  return element.nodeName.toLowerCase() == 'input';\n}\n/** Gets whether an element is an anchor element. */\nfunction isAnchorElement(element) {\n  return element.nodeName.toLowerCase() == 'a';\n}\n/** Gets whether an element has a valid tabindex. */\nfunction hasValidTabIndex(element) {\n  if (!element.hasAttribute('tabindex') || element.tabIndex === undefined) {\n    return false;\n  }\n  let tabIndex = element.getAttribute('tabindex');\n  return !!(tabIndex && !isNaN(parseInt(tabIndex, 10)));\n}\n/**\n * Returns the parsed tabindex from the element attributes instead of returning the\n * evaluated tabindex from the browsers defaults.\n */\nfunction getTabIndexValue(element) {\n  if (!hasValidTabIndex(element)) {\n    return null;\n  }\n  // See browser issue in Gecko https://bugzilla.mozilla.org/show_bug.cgi?id=1128054\n  const tabIndex = parseInt(element.getAttribute('tabindex') || '', 10);\n  return isNaN(tabIndex) ? -1 : tabIndex;\n}\n/** Checks whether the specified element is potentially tabbable on iOS */\nfunction isPotentiallyTabbableIOS(element) {\n  let nodeName = element.nodeName.toLowerCase();\n  let inputType = nodeName === 'input' && element.type;\n  return inputType === 'text' || inputType === 'password' || nodeName === 'select' || nodeName === 'textarea';\n}\n/**\n * Gets whether an element is potentially focusable without taking current visible/disabled state\n * into account.\n */\nfunction isPotentiallyFocusable(element) {\n  // Inputs are potentially focusable *unless* they're type=\"hidden\".\n  if (isHiddenInput(element)) {\n    return false;\n  }\n  return isNativeFormElement(element) || isAnchorWithHref(element) || element.hasAttribute('contenteditable') || hasValidTabIndex(element);\n}\n/** Gets the parent window of a DOM node with regards of being inside of an iframe. */\nfunction getWindow(node) {\n  // ownerDocument is null if `node` itself *is* a document.\n  return node.ownerDocument && node.ownerDocument.defaultView || window;\n}\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class currently uses a relatively simple approach to focus trapping.\n * It assumes that the tab order is the same as DOM order, which is not necessarily true.\n * Things like `tabIndex > 0`, flex `order`, and shadow roots can cause the two to be misaligned.\n */\nclass FocusTrap {\n  _element;\n  _checker;\n  _ngZone;\n  _document;\n  _injector;\n  _startAnchor;\n  _endAnchor;\n  _hasAttached = false;\n  // Event listeners for the anchors. Need to be regular functions so that we can unbind them later.\n  startAnchorListener = () => this.focusLastTabbableElement();\n  endAnchorListener = () => this.focusFirstTabbableElement();\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(value, this._startAnchor);\n      this._toggleAnchorTabIndex(value, this._endAnchor);\n    }\n  }\n  _enabled = true;\n  constructor(_element, _checker, _ngZone, _document, deferAnchors = false, /** @breaking-change 20.0.0 param to become required */\n  _injector) {\n    this._element = _element;\n    this._checker = _checker;\n    this._ngZone = _ngZone;\n    this._document = _document;\n    this._injector = _injector;\n    if (!deferAnchors) {\n      this.attachAnchors();\n    }\n  }\n  /** Destroys the focus trap by cleaning up the anchors. */\n  destroy() {\n    const startAnchor = this._startAnchor;\n    const endAnchor = this._endAnchor;\n    if (startAnchor) {\n      startAnchor.removeEventListener('focus', this.startAnchorListener);\n      startAnchor.remove();\n    }\n    if (endAnchor) {\n      endAnchor.removeEventListener('focus', this.endAnchorListener);\n      endAnchor.remove();\n    }\n    this._startAnchor = this._endAnchor = null;\n    this._hasAttached = false;\n  }\n  /**\n   * Inserts the anchors into the DOM. This is usually done automatically\n   * in the constructor, but can be deferred for cases like directives with `*ngIf`.\n   * @returns Whether the focus trap managed to attach successfully. This may not be the case\n   * if the target element isn't currently in the DOM.\n   */\n  attachAnchors() {\n    // If we're not on the browser, there can be no focus to trap.\n    if (this._hasAttached) {\n      return true;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      if (!this._startAnchor) {\n        this._startAnchor = this._createAnchor();\n        this._startAnchor.addEventListener('focus', this.startAnchorListener);\n      }\n      if (!this._endAnchor) {\n        this._endAnchor = this._createAnchor();\n        this._endAnchor.addEventListener('focus', this.endAnchorListener);\n      }\n    });\n    if (this._element.parentNode) {\n      this._element.parentNode.insertBefore(this._startAnchor, this._element);\n      this._element.parentNode.insertBefore(this._endAnchor, this._element.nextSibling);\n      this._hasAttached = true;\n    }\n    return this._hasAttached;\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses the first tabbable element.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusInitialElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusInitialElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the first tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusFirstTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusFirstTabbableElement(options)));\n    });\n  }\n  /**\n   * Waits for the zone to stabilize, then focuses\n   * the last tabbable element within the focus trap region.\n   * @returns Returns a promise that resolves with a boolean, depending\n   * on whether focus was moved successfully.\n   */\n  focusLastTabbableElementWhenReady(options) {\n    return new Promise(resolve => {\n      this._executeOnStable(() => resolve(this.focusLastTabbableElement(options)));\n    });\n  }\n  /**\n   * Get the specified boundary element of the trapped region.\n   * @param bound The boundary to get (start or end of trapped region).\n   * @returns The boundary element.\n   */\n  _getRegionBoundary(bound) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const markers = this._element.querySelectorAll(`[cdk-focus-region-${bound}], ` + `[cdkFocusRegion${bound}], ` + `[cdk-focus-${bound}]`);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      for (let i = 0; i < markers.length; i++) {\n        // @breaking-change 8.0.0\n        if (markers[i].hasAttribute(`cdk-focus-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated ` + `attribute will be removed in 8.0.0.`, markers[i]);\n        } else if (markers[i].hasAttribute(`cdk-focus-region-${bound}`)) {\n          console.warn(`Found use of deprecated attribute 'cdk-focus-region-${bound}', ` + `use 'cdkFocusRegion${bound}' instead. The deprecated attribute ` + `will be removed in 8.0.0.`, markers[i]);\n        }\n      }\n    }\n    if (bound == 'start') {\n      return markers.length ? markers[0] : this._getFirstTabbableElement(this._element);\n    }\n    return markers.length ? markers[markers.length - 1] : this._getLastTabbableElement(this._element);\n  }\n  /**\n   * Focuses the element that should be focused when the focus trap is initialized.\n   * @returns Whether focus was moved successfully.\n   */\n  focusInitialElement(options) {\n    // Contains the deprecated version of selector, for temporary backwards comparability.\n    const redirectToElement = this._element.querySelector(`[cdk-focus-initial], ` + `[cdkFocusInitial]`);\n    if (redirectToElement) {\n      // @breaking-change 8.0.0\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && redirectToElement.hasAttribute(`cdk-focus-initial`)) {\n        console.warn(`Found use of deprecated attribute 'cdk-focus-initial', ` + `use 'cdkFocusInitial' instead. The deprecated attribute ` + `will be removed in 8.0.0`, redirectToElement);\n      }\n      // Warn the consumer if the element they've pointed to\n      // isn't focusable, when not in production mode.\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._checker.isFocusable(redirectToElement)) {\n        console.warn(`Element matching '[cdkFocusInitial]' is not focusable.`, redirectToElement);\n      }\n      if (!this._checker.isFocusable(redirectToElement)) {\n        const focusableChild = this._getFirstTabbableElement(redirectToElement);\n        focusableChild?.focus(options);\n        return !!focusableChild;\n      }\n      redirectToElement.focus(options);\n      return true;\n    }\n    return this.focusFirstTabbableElement(options);\n  }\n  /**\n   * Focuses the first tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusFirstTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('start');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Focuses the last tabbable element within the focus trap region.\n   * @returns Whether focus was moved successfully.\n   */\n  focusLastTabbableElement(options) {\n    const redirectToElement = this._getRegionBoundary('end');\n    if (redirectToElement) {\n      redirectToElement.focus(options);\n    }\n    return !!redirectToElement;\n  }\n  /**\n   * Checks whether the focus trap has successfully been attached.\n   */\n  hasAttached() {\n    return this._hasAttached;\n  }\n  /** Get the first tabbable element from a DOM subtree (inclusive). */\n  _getFirstTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    const children = root.children;\n    for (let i = 0; i < children.length; i++) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getFirstTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Get the last tabbable element from a DOM subtree (inclusive). */\n  _getLastTabbableElement(root) {\n    if (this._checker.isFocusable(root) && this._checker.isTabbable(root)) {\n      return root;\n    }\n    // Iterate in reverse DOM order.\n    const children = root.children;\n    for (let i = children.length - 1; i >= 0; i--) {\n      const tabbableChild = children[i].nodeType === this._document.ELEMENT_NODE ? this._getLastTabbableElement(children[i]) : null;\n      if (tabbableChild) {\n        return tabbableChild;\n      }\n    }\n    return null;\n  }\n  /** Creates an anchor element. */\n  _createAnchor() {\n    const anchor = this._document.createElement('div');\n    this._toggleAnchorTabIndex(this._enabled, anchor);\n    anchor.classList.add('cdk-visually-hidden');\n    anchor.classList.add('cdk-focus-trap-anchor');\n    anchor.setAttribute('aria-hidden', 'true');\n    return anchor;\n  }\n  /**\n   * Toggles the `tabindex` of an anchor, based on the enabled state of the focus trap.\n   * @param isEnabled Whether the focus trap is enabled.\n   * @param anchor Anchor on which to toggle the tabindex.\n   */\n  _toggleAnchorTabIndex(isEnabled, anchor) {\n    // Remove the tabindex completely, rather than setting it to -1, because if the\n    // element has a tabindex, the user might still hit it when navigating with the arrow keys.\n    isEnabled ? anchor.setAttribute('tabindex', '0') : anchor.removeAttribute('tabindex');\n  }\n  /**\n   * Toggles the`tabindex` of both anchors to either trap Tab focus or allow it to escape.\n   * @param enabled: Whether the anchors should trap Tab.\n   */\n  toggleAnchors(enabled) {\n    if (this._startAnchor && this._endAnchor) {\n      this._toggleAnchorTabIndex(enabled, this._startAnchor);\n      this._toggleAnchorTabIndex(enabled, this._endAnchor);\n    }\n  }\n  /** Executes a function when the zone is stable. */\n  _executeOnStable(fn) {\n    // TODO: remove this conditional when injector is required in the constructor.\n    if (this._injector) {\n      afterNextRender(fn, {\n        injector: this._injector\n      });\n    } else {\n      setTimeout(fn);\n    }\n  }\n}\n/**\n * Factory that allows easy instantiation of focus traps.\n */\nclass FocusTrapFactory {\n  _checker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _document = inject(DOCUMENT);\n  _injector = inject(Injector);\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param deferCaptureElements Defers the creation of focus-capturing elements to be done\n   *     manually by the user.\n   * @returns The created focus trap instance.\n   */\n  create(element, deferCaptureElements = false) {\n    return new FocusTrap(element, this._checker, this._ngZone, this._document, deferCaptureElements, this._injector);\n  }\n  static ɵfac = function FocusTrapFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrapFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapFactory,\n    factory: FocusTrapFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** Directive for trapping focus within a region. */\nclass CdkTrapFocus {\n  _elementRef = inject(ElementRef);\n  _focusTrapFactory = inject(FocusTrapFactory);\n  /** Underlying FocusTrap instance. */\n  focusTrap;\n  /** Previously focused element to restore focus to upon destroy when using autoCapture. */\n  _previouslyFocusedElement = null;\n  /** Whether the focus trap is active. */\n  get enabled() {\n    return this.focusTrap?.enabled || false;\n  }\n  set enabled(value) {\n    if (this.focusTrap) {\n      this.focusTrap.enabled = value;\n    }\n  }\n  /**\n   * Whether the directive should automatically move focus into the trapped region upon\n   * initialization and return focus to the previous activeElement upon destruction.\n   */\n  autoCapture;\n  constructor() {\n    const platform = inject(Platform);\n    if (platform.isBrowser) {\n      this.focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement, true);\n    }\n  }\n  ngOnDestroy() {\n    this.focusTrap?.destroy();\n    // If we stored a previously focused element when using autoCapture, return focus to that\n    // element now that the trapped region is being destroyed.\n    if (this._previouslyFocusedElement) {\n      this._previouslyFocusedElement.focus();\n      this._previouslyFocusedElement = null;\n    }\n  }\n  ngAfterContentInit() {\n    this.focusTrap?.attachAnchors();\n    if (this.autoCapture) {\n      this._captureFocus();\n    }\n  }\n  ngDoCheck() {\n    if (this.focusTrap && !this.focusTrap.hasAttached()) {\n      this.focusTrap.attachAnchors();\n    }\n  }\n  ngOnChanges(changes) {\n    const autoCaptureChange = changes['autoCapture'];\n    if (autoCaptureChange && !autoCaptureChange.firstChange && this.autoCapture && this.focusTrap?.hasAttached()) {\n      this._captureFocus();\n    }\n  }\n  _captureFocus() {\n    this._previouslyFocusedElement = _getFocusedElementPierceShadowDom();\n    this.focusTrap?.focusInitialElementWhenReady();\n  }\n  static ɵfac = function CdkTrapFocus_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTrapFocus)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTrapFocus,\n    selectors: [[\"\", \"cdkTrapFocus\", \"\"]],\n    inputs: {\n      enabled: [2, \"cdkTrapFocus\", \"enabled\", booleanAttribute],\n      autoCapture: [2, \"cdkTrapFocusAutoCapture\", \"autoCapture\", booleanAttribute]\n    },\n    exportAs: [\"cdkTrapFocus\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTrapFocus, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkTrapFocus]',\n      exportAs: 'cdkTrapFocus'\n    }]\n  }], () => [], {\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocus',\n        transform: booleanAttribute\n      }]\n    }],\n    autoCapture: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTrapFocusAutoCapture',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nconst LIVE_ANNOUNCER_ELEMENT_TOKEN = new InjectionToken('liveAnnouncerElement', {\n  providedIn: 'root',\n  factory: LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY() {\n  return null;\n}\n/** Injection token that can be used to configure the default options for the LiveAnnouncer. */\nconst LIVE_ANNOUNCER_DEFAULT_OPTIONS = new InjectionToken('LIVE_ANNOUNCER_DEFAULT_OPTIONS');\nlet uniqueIds = 0;\nclass LiveAnnouncer {\n  _ngZone = inject(NgZone);\n  _defaultOptions = inject(LIVE_ANNOUNCER_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _liveElement;\n  _document = inject(DOCUMENT);\n  _previousTimeout;\n  _currentPromise;\n  _currentResolve;\n  constructor() {\n    const elementToken = inject(LIVE_ANNOUNCER_ELEMENT_TOKEN, {\n      optional: true\n    });\n    this._liveElement = elementToken || this._createLiveElement();\n  }\n  announce(message, ...args) {\n    const defaultOptions = this._defaultOptions;\n    let politeness;\n    let duration;\n    if (args.length === 1 && typeof args[0] === 'number') {\n      duration = args[0];\n    } else {\n      [politeness, duration] = args;\n    }\n    this.clear();\n    clearTimeout(this._previousTimeout);\n    if (!politeness) {\n      politeness = defaultOptions && defaultOptions.politeness ? defaultOptions.politeness : 'polite';\n    }\n    if (duration == null && defaultOptions) {\n      duration = defaultOptions.duration;\n    }\n    // TODO: ensure changing the politeness works on all environments we support.\n    this._liveElement.setAttribute('aria-live', politeness);\n    if (this._liveElement.id) {\n      this._exposeAnnouncerToModals(this._liveElement.id);\n    }\n    // This 100ms timeout is necessary for some browser + screen-reader combinations:\n    // - Both JAWS and NVDA over IE11 will not announce anything without a non-zero timeout.\n    // - With Chrome and IE11 with NVDA or JAWS, a repeated (identical) message won't be read a\n    //   second time without clearing and then using a non-zero delay.\n    // (using JAWS 17 at time of this writing).\n    return this._ngZone.runOutsideAngular(() => {\n      if (!this._currentPromise) {\n        this._currentPromise = new Promise(resolve => this._currentResolve = resolve);\n      }\n      clearTimeout(this._previousTimeout);\n      this._previousTimeout = setTimeout(() => {\n        this._liveElement.textContent = message;\n        if (typeof duration === 'number') {\n          this._previousTimeout = setTimeout(() => this.clear(), duration);\n        }\n        // For some reason in tests this can be undefined\n        // Probably related to ZoneJS and every other thing that patches browser APIs in tests\n        this._currentResolve?.();\n        this._currentPromise = this._currentResolve = undefined;\n      }, 100);\n      return this._currentPromise;\n    });\n  }\n  /**\n   * Clears the current text from the announcer element. Can be used to prevent\n   * screen readers from reading the text out again while the user is going\n   * through the page landmarks.\n   */\n  clear() {\n    if (this._liveElement) {\n      this._liveElement.textContent = '';\n    }\n  }\n  ngOnDestroy() {\n    clearTimeout(this._previousTimeout);\n    this._liveElement?.remove();\n    this._liveElement = null;\n    this._currentResolve?.();\n    this._currentPromise = this._currentResolve = undefined;\n  }\n  _createLiveElement() {\n    const elementClass = 'cdk-live-announcer-element';\n    const previousElements = this._document.getElementsByClassName(elementClass);\n    const liveEl = this._document.createElement('div');\n    // Remove any old containers. This can happen when coming in from a server-side-rendered page.\n    for (let i = 0; i < previousElements.length; i++) {\n      previousElements[i].remove();\n    }\n    liveEl.classList.add(elementClass);\n    liveEl.classList.add('cdk-visually-hidden');\n    liveEl.setAttribute('aria-atomic', 'true');\n    liveEl.setAttribute('aria-live', 'polite');\n    liveEl.id = `cdk-live-announcer-${uniqueIds++}`;\n    this._document.body.appendChild(liveEl);\n    return liveEl;\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live announcer element if there is an\n   * `aria-modal` and the live announcer is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live announcer element.\n   */\n  _exposeAnnouncerToModals(id) {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `SnakBarContainer` and other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  static ɵfac = function LiveAnnouncer_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LiveAnnouncer)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LiveAnnouncer,\n    factory: LiveAnnouncer.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LiveAnnouncer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * A directive that works similarly to aria-live, but uses the LiveAnnouncer to ensure compatibility\n * with a wider range of browsers and screen readers.\n */\nclass CdkAriaLive {\n  _elementRef = inject(ElementRef);\n  _liveAnnouncer = inject(LiveAnnouncer);\n  _contentObserver = inject(ContentObserver);\n  _ngZone = inject(NgZone);\n  /** The aria-live politeness level to use when announcing messages. */\n  get politeness() {\n    return this._politeness;\n  }\n  set politeness(value) {\n    this._politeness = value === 'off' || value === 'assertive' ? value : 'polite';\n    if (this._politeness === 'off') {\n      if (this._subscription) {\n        this._subscription.unsubscribe();\n        this._subscription = null;\n      }\n    } else if (!this._subscription) {\n      this._subscription = this._ngZone.runOutsideAngular(() => {\n        return this._contentObserver.observe(this._elementRef).subscribe(() => {\n          // Note that we use textContent here, rather than innerText, in order to avoid a reflow.\n          const elementText = this._elementRef.nativeElement.textContent;\n          // The `MutationObserver` fires also for attribute\n          // changes which we don't want to announce.\n          if (elementText !== this._previousAnnouncedText) {\n            this._liveAnnouncer.announce(elementText, this._politeness, this.duration);\n            this._previousAnnouncedText = elementText;\n          }\n        });\n      });\n    }\n  }\n  _politeness = 'polite';\n  /** Time in milliseconds after which to clear out the announcer element. */\n  duration;\n  _previousAnnouncedText;\n  _subscription;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n  }\n  ngOnDestroy() {\n    if (this._subscription) {\n      this._subscription.unsubscribe();\n    }\n  }\n  static ɵfac = function CdkAriaLive_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkAriaLive)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAriaLive,\n    selectors: [[\"\", \"cdkAriaLive\", \"\"]],\n    inputs: {\n      politeness: [0, \"cdkAriaLive\", \"politeness\"],\n      duration: [0, \"cdkAriaLiveDuration\", \"duration\"]\n    },\n    exportAs: [\"cdkAriaLive\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAriaLive, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAriaLive]',\n      exportAs: 'cdkAriaLive'\n    }]\n  }], () => [], {\n    politeness: [{\n      type: Input,\n      args: ['cdkAriaLive']\n    }],\n    duration: [{\n      type: Input,\n      args: ['cdkAriaLiveDuration']\n    }]\n  });\n})();\n\n/** Set of possible high-contrast mode backgrounds. */\nvar HighContrastMode;\n(function (HighContrastMode) {\n  HighContrastMode[HighContrastMode[\"NONE\"] = 0] = \"NONE\";\n  HighContrastMode[HighContrastMode[\"BLACK_ON_WHITE\"] = 1] = \"BLACK_ON_WHITE\";\n  HighContrastMode[HighContrastMode[\"WHITE_ON_BLACK\"] = 2] = \"WHITE_ON_BLACK\";\n})(HighContrastMode || (HighContrastMode = {}));\n/** CSS class applied to the document body when in black-on-white high-contrast mode. */\nconst BLACK_ON_WHITE_CSS_CLASS = 'cdk-high-contrast-black-on-white';\n/** CSS class applied to the document body when in white-on-black high-contrast mode. */\nconst WHITE_ON_BLACK_CSS_CLASS = 'cdk-high-contrast-white-on-black';\n/** CSS class applied to the document body when in high-contrast mode. */\nconst HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS = 'cdk-high-contrast-active';\n/**\n * Service to determine whether the browser is currently in a high-contrast-mode environment.\n *\n * Microsoft Windows supports an accessibility feature called \"High Contrast Mode\". This mode\n * changes the appearance of all applications, including web applications, to dramatically increase\n * contrast.\n *\n * IE, Edge, and Firefox currently support this mode. Chrome does not support Windows High Contrast\n * Mode. This service does not detect high-contrast mode as added by the Chrome \"High Contrast\"\n * browser extension.\n */\nclass HighContrastModeDetector {\n  _platform = inject(Platform);\n  /**\n   * Figuring out the high contrast mode and adding the body classes can cause\n   * some expensive layouts. This flag is used to ensure that we only do it once.\n   */\n  _hasCheckedHighContrastMode;\n  _document = inject(DOCUMENT);\n  _breakpointSubscription;\n  constructor() {\n    this._breakpointSubscription = inject(BreakpointObserver).observe('(forced-colors: active)').subscribe(() => {\n      if (this._hasCheckedHighContrastMode) {\n        this._hasCheckedHighContrastMode = false;\n        this._applyBodyHighContrastModeCssClasses();\n      }\n    });\n  }\n  /** Gets the current high-contrast-mode for the page. */\n  getHighContrastMode() {\n    if (!this._platform.isBrowser) {\n      return HighContrastMode.NONE;\n    }\n    // Create a test element with an arbitrary background-color that is neither black nor\n    // white; high-contrast mode will coerce the color to either black or white. Also ensure that\n    // appending the test element to the DOM does not affect layout by absolutely positioning it\n    const testElement = this._document.createElement('div');\n    testElement.style.backgroundColor = 'rgb(1,2,3)';\n    testElement.style.position = 'absolute';\n    this._document.body.appendChild(testElement);\n    // Get the computed style for the background color, collapsing spaces to normalize between\n    // browsers. Once we get this color, we no longer need the test element. Access the `window`\n    // via the document so we can fake it in tests. Note that we have extra null checks, because\n    // this logic will likely run during app bootstrap and throwing can break the entire app.\n    const documentWindow = this._document.defaultView || window;\n    const computedStyle = documentWindow && documentWindow.getComputedStyle ? documentWindow.getComputedStyle(testElement) : null;\n    const computedColor = (computedStyle && computedStyle.backgroundColor || '').replace(/ /g, '');\n    testElement.remove();\n    switch (computedColor) {\n      // Pre Windows 11 dark theme.\n      case 'rgb(0,0,0)':\n      // Windows 11 dark themes.\n      case 'rgb(45,50,54)':\n      case 'rgb(32,32,32)':\n        return HighContrastMode.WHITE_ON_BLACK;\n      // Pre Windows 11 light theme.\n      case 'rgb(255,255,255)':\n      // Windows 11 light theme.\n      case 'rgb(255,250,239)':\n        return HighContrastMode.BLACK_ON_WHITE;\n    }\n    return HighContrastMode.NONE;\n  }\n  ngOnDestroy() {\n    this._breakpointSubscription.unsubscribe();\n  }\n  /** Applies CSS classes indicating high-contrast mode to document body (browser-only). */\n  _applyBodyHighContrastModeCssClasses() {\n    if (!this._hasCheckedHighContrastMode && this._platform.isBrowser && this._document.body) {\n      const bodyClasses = this._document.body.classList;\n      bodyClasses.remove(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      this._hasCheckedHighContrastMode = true;\n      const mode = this.getHighContrastMode();\n      if (mode === HighContrastMode.BLACK_ON_WHITE) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, BLACK_ON_WHITE_CSS_CLASS);\n      } else if (mode === HighContrastMode.WHITE_ON_BLACK) {\n        bodyClasses.add(HIGH_CONTRAST_MODE_ACTIVE_CSS_CLASS, WHITE_ON_BLACK_CSS_CLASS);\n      }\n    }\n  }\n  static ɵfac = function HighContrastModeDetector_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || HighContrastModeDetector)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: HighContrastModeDetector,\n    factory: HighContrastModeDetector.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HighContrastModeDetector, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nclass A11yModule {\n  constructor() {\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n  static ɵfac = function A11yModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || A11yModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: A11yModule,\n    imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n    exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ObserversModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(A11yModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ObserversModule, CdkAriaLive, CdkTrapFocus, CdkMonitorFocus],\n      exports: [CdkAriaLive, CdkTrapFocus, CdkMonitorFocus]\n    }]\n  }], () => [], null);\n})();\nexport { A11yModule as A, CdkTrapFocus as C, FocusTrap as F, HighContrastModeDetector as H, InteractivityChecker as I, LiveAnnouncer as L, HighContrastMode as a, FocusTrapFactory as b, IsFocusableConfig as c, CdkAriaLive as d, LIVE_ANNOUNCER_ELEMENT_TOKEN as e, LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY as f, LIVE_ANNOUNCER_DEFAULT_OPTIONS as g };\n", "import * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable } from '@angular/core';\n\n/**\n * Keeps track of the ID count per prefix. This helps us make the IDs a bit more deterministic\n * like they were before the service was introduced. Note that ideally we wouldn't have to do\n * this, but there are some internal tests that rely on the IDs.\n */\nconst counters = {};\n/** Service that generates unique IDs for DOM nodes. */\nclass _IdGenerator {\n  _appId = inject(APP_ID);\n  /**\n   * Generates a unique ID with a specific prefix.\n   * @param prefix Prefix to add to the ID.\n   */\n  getId(prefix) {\n    // Omit the app ID if it's the default `ng`. Since the vast majority of pages have one\n    // Angular app on them, we can reduce the amount of breakages by not adding it.\n    if (this._appId !== 'ng') {\n      prefix += this._appId;\n    }\n    if (!counters.hasOwnProperty(prefix)) {\n      counters[prefix] = 0;\n    }\n    return `${prefix}${counters[prefix]++}`;\n  }\n  static ɵfac = function _IdGenerator_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _IdGenerator)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: _IdGenerator,\n    factory: _IdGenerator.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_IdGenerator, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nexport { _IdGenerator as _ };\n", "/**\n * Checks whether a modifier key is pressed.\n * @param event Event to be checked.\n */\nfunction hasModifierKey(event, ...modifiers) {\n  if (modifiers.length) {\n    return modifiers.some(modifier => event[modifier]);\n  }\n  return event.altKey || event.shiftKey || event.ctrlKey || event.metaKey;\n}\nexport { hasModifierKey as h };\n", "import { signal, QueryList, isSignal, effect } from '@angular/core';\nimport { Subscription, Subject } from 'rxjs';\nimport { T as Typeahead } from './typeahead-0113d27c.mjs';\nimport { h as hasModifierKey } from './modifiers-3e8908bb.mjs';\nimport { P as PAGE_DOWN, b as PAGE_UP, E as END, H as HOME, L as LEFT_ARROW, R as RIGHT_ARROW, U as UP_ARROW, D as DOWN_ARROW, T as TAB } from './keycodes-0e4398c6.mjs';\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nclass ListKeyManager {\n  _items;\n  _activeItemIndex = -1;\n  _activeItem = signal(null);\n  _wrap = false;\n  _typeaheadSubscription = Subscription.EMPTY;\n  _itemChangesSubscription;\n  _vertical = true;\n  _horizontal;\n  _allowedModifierKeys = [];\n  _homeAndEnd = false;\n  _pageUpAndDown = {\n    enabled: false,\n    delta: 10\n  };\n  _effectRef;\n  _typeahead;\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager. By default, disabled items are skipped.\n   */\n  _skipPredicateFn = item => item.disabled;\n  constructor(_items, injector) {\n    this._items = _items;\n    // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (_items instanceof QueryList) {\n      this._itemChangesSubscription = _items.changes.subscribe(newItems => this._itemsChanged(newItems.toArray()));\n    } else if (isSignal(_items)) {\n      if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error('ListKeyManager constructed with a signal must receive an injector');\n      }\n      this._effectRef = effect(() => this._itemsChanged(_items()), {\n        injector\n      });\n    }\n  }\n  /**\n   * Stream that emits any time the TAB key is pressed, so components can react\n   * when focus is shifted off of the list.\n   */\n  tabOut = new Subject();\n  /** Stream that emits whenever the active item of the list manager changes. */\n  change = new Subject();\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate) {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n  withWrap(shouldWrap = true) {\n    this._wrap = shouldWrap;\n    return this;\n  }\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled = true) {\n    this._vertical = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction) {\n    this._horizontal = direction;\n    return this;\n  }\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n  withAllowedModifierKeys(keys) {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval = 200) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const items = this._getItemsArray();\n      if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n        throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n      }\n    }\n    this._typeaheadSubscription.unsubscribe();\n    const items = this._getItemsArray();\n    this._typeahead = new Typeahead(items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.setActiveItem(item);\n    });\n    return this;\n  }\n  /** Cancels the current typeahead sequence. */\n  cancelTypeahead() {\n    this._typeahead?.reset();\n    return this;\n  }\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n  withHomeAndEnd(enabled = true) {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n  /**\n   * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n   * respectively when the Page-Up or Page-Down key is pressed.\n   * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n   * @param delta Whether pressing the Home or End key activates the first/last item.\n   */\n  withPageUpDown(enabled = true, delta = 10) {\n    this._pageUpAndDown = {\n      enabled,\n      delta\n    };\n    return this;\n  }\n  setActiveItem(item) {\n    const previousActiveItem = this._activeItem();\n    this.updateActiveItem(item);\n    if (this._activeItem() !== previousActiveItem) {\n      this.change.next(this._activeItemIndex);\n    }\n  }\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event) {\n    const keyCode = event.keyCode;\n    const modifiers = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n      case PAGE_UP:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex - this._pageUpAndDown.delta;\n          this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n          break;\n        } else {\n          return;\n        }\n      case PAGE_DOWN:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex + this._pageUpAndDown.delta;\n          const itemsLength = this._getItemsArray().length;\n          this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n          break;\n        } else {\n          return;\n        }\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          this._typeahead?.handleKey(event);\n        }\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  get activeItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The active item. */\n  get activeItem() {\n    return this._activeItem();\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return !!this._typeahead && this._typeahead.isTyping();\n  }\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive() {\n    this._setActiveItemByIndex(0, 1);\n  }\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive() {\n    this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n  }\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive() {\n    this._activeItemIndex < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive() {\n    this._activeItemIndex < 0 && this._wrap ? this.setLastItemActive() : this._setActiveItemByDelta(-1);\n  }\n  updateActiveItem(item) {\n    const itemArray = this._getItemsArray();\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index];\n    // Explicitly check for `null` and `undefined` because other falsy values are valid.\n    this._activeItem.set(activeItem == null ? null : activeItem);\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n  }\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._itemChangesSubscription?.unsubscribe();\n    this._effectRef?.destroy();\n    this._typeahead?.destroy();\n    this.tabOut.complete();\n    this.change.complete();\n  }\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  _setActiveItemByDelta(delta) {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  _setActiveInWrapMode(delta) {\n    const items = this._getItemsArray();\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex + delta * i + items.length) % items.length;\n      const item = items[index];\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  _setActiveInDefaultMode(delta) {\n    this._setActiveItemByIndex(this._activeItemIndex + delta, delta);\n  }\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  _setActiveItemByIndex(index, fallbackDelta) {\n    const items = this._getItemsArray();\n    if (!items[index]) {\n      return;\n    }\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n      if (!items[index]) {\n        return;\n      }\n    }\n    this.setActiveItem(index);\n  }\n  /** Returns the items as an array. */\n  _getItemsArray() {\n    if (isSignal(this._items)) {\n      return this._items();\n    }\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n  /** Callback for when the items have changed. */\n  _itemsChanged(newItems) {\n    this._typeahead?.setItems(newItems);\n    const activeItem = this._activeItem();\n    if (activeItem) {\n      const newIndex = newItems.indexOf(activeItem);\n      if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n        this._activeItemIndex = newIndex;\n        this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n      }\n    }\n  }\n}\nexport { ListKeyManager as L };\n", "import { Subject } from 'rxjs';\nimport { tap, debounceTime, filter, map } from 'rxjs/operators';\nimport { c as A, Z, d as ZERO, N as NINE } from './keycodes-0e4398c6.mjs';\nconst DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS = 200;\n/**\n * Selects items based on keyboard inputs. Implements the typeahead functionality of\n * `role=\"listbox\"` or `role=\"tree\"` and other related roles.\n */\nclass Typeahead {\n  _letterKeyStream = new Subject();\n  _items = [];\n  _selectedItemIndex = -1;\n  /** Buffer for the letters that the user has pressed */\n  _pressedLetters = [];\n  _skipPredicateFn;\n  _selectedItem = new Subject();\n  selectedItem = this._selectedItem;\n  constructor(initialItems, config) {\n    const typeAheadInterval = typeof config?.debounceInterval === 'number' ? config.debounceInterval : DEFAULT_TYPEAHEAD_DEBOUNCE_INTERVAL_MS;\n    if (config?.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && initialItems.length && initialItems.some(item => typeof item.getLabel !== 'function')) {\n      throw new Error('KeyManager items in typeahead mode must implement the `getLabel` method.');\n    }\n    this.setItems(initialItems);\n    this._setupKeyHandler(typeAheadInterval);\n  }\n  destroy() {\n    this._pressedLetters = [];\n    this._letterKeyStream.complete();\n    this._selectedItem.complete();\n  }\n  setCurrentSelectedItemIndex(index) {\n    this._selectedItemIndex = index;\n  }\n  setItems(items) {\n    this._items = items;\n  }\n  handleKey(event) {\n    const keyCode = event.keyCode;\n    // Attempt to use the `event.key` which also maps it to the user's keyboard language,\n    // otherwise fall back to resolving alphanumeric characters via the keyCode.\n    if (event.key && event.key.length === 1) {\n      this._letterKeyStream.next(event.key.toLocaleUpperCase());\n    } else if (keyCode >= A && keyCode <= Z || keyCode >= ZERO && keyCode <= NINE) {\n      this._letterKeyStream.next(String.fromCharCode(keyCode));\n    }\n  }\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping() {\n    return this._pressedLetters.length > 0;\n  }\n  /** Resets the currently stored sequence of typed letters. */\n  reset() {\n    this._pressedLetters = [];\n  }\n  _setupKeyHandler(typeAheadInterval) {\n    // Debounce the presses of non-navigational keys, collect the ones that correspond to letters\n    // and convert those letters back into a string. Afterwards find the first item that starts\n    // with that string and select it.\n    this._letterKeyStream.pipe(tap(letter => this._pressedLetters.push(letter)), debounceTime(typeAheadInterval), filter(() => this._pressedLetters.length > 0), map(() => this._pressedLetters.join('').toLocaleUpperCase())).subscribe(inputString => {\n      // Start at 1 because we want to start searching at the item immediately\n      // following the current active item.\n      for (let i = 1; i < this._items.length + 1; i++) {\n        const index = (this._selectedItemIndex + i) % this._items.length;\n        const item = this._items[index];\n        if (!this._skipPredicateFn?.(item) && item.getLabel?.().toLocaleUpperCase().trim().indexOf(inputString) === 0) {\n          this._selectedItem.next(item);\n          break;\n        }\n      }\n      this._pressedLetters = [];\n    });\n  }\n}\nexport { Typeahead as T };\n", "import { L as ListKeyManager } from './list-key-manager-f9c3e90c.mjs';\nclass ActiveDescendantKeyManager extends ListKeyManager {\n  setActiveItem(index) {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n}\nexport { ActiveDescendantKeyManager as A };\n", "export { C as CdkMonitorFocus, c as FOCUS_MONITOR_DEFAULT_OPTIONS, d as FocusMonitor, F as FocusMonitorDetectionMode, a as INPUT_MODALITY_DETECTOR_DEFAULT_OPTIONS, b as INPUT_MODALITY_DETECTOR_OPTIONS, I as InputModalityDetector } from './focus-monitor-28b6c826.mjs';\nimport { F as FocusTrap, I as InteractivityChecker } from './a11y-module-e500b0f2.mjs';\nexport { A as A11yModule, d as CdkAriaLive, C as CdkTrapFocus, F as FocusTrap, b as FocusTrapFactory, a as HighContrastMode, H as HighContrastModeDetector, I as InteractivityChecker, c as IsFocusableConfig, g as LIVE_ANNOUNCER_DEFAULT_OPTIONS, e as LIVE_ANNOUNCER_ELEMENT_TOKEN, f as LIVE_ANNOUNCER_ELEMENT_TOKEN_FACTORY, L as LiveAnnouncer } from './a11y-module-e500b0f2.mjs';\nexport { _ as _IdGenerator } from './id-generator-0b91c6f7.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, APP_ID, Injectable, InjectionToken, NgZone, Injector } from '@angular/core';\nimport { P as Platform } from './platform-20fc4de8.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-09eecacc.mjs';\nimport { _ as _VisuallyHiddenLoader } from './visually-hidden-9a93b8bb.mjs';\nexport { A as ActiveDescendantKeyManager } from './activedescendant-key-manager-f0c079ca.mjs';\nexport { F as FocusKeyManager } from './focus-key-manager-ff488563.mjs';\nexport { L as ListKeyManager } from './list-key-manager-f9c3e90c.mjs';\nimport { Subject } from 'rxjs';\nimport { T as TREE_KEY_MANAGER } from './tree-key-manager-1212bcbe.mjs';\nexport { T as TREE_KEY_MANAGER, b as TREE_KEY_MANAGER_FACTORY, c as TREE_KEY_MANAGER_FACTORY_PROVIDER, a as TreeKeyManager } from './tree-key-manager-1212bcbe.mjs';\nexport { i as isFakeMousedownFromScreenReader, a as isFakeTouchstartFromScreenReader } from './fake-event-detection-84590b88.mjs';\nimport 'rxjs/operators';\nimport './keycodes-0e4398c6.mjs';\nimport './shadow-dom-318658ae.mjs';\nimport './backwards-compatibility-08253a84.mjs';\nimport './passive-listeners-93cf8be8.mjs';\nimport './element-15999318.mjs';\nimport './breakpoints-observer-8e7409df.mjs';\nimport './array-6239d2f8.mjs';\nimport './observe-content-937cdfbe.mjs';\nimport './typeahead-0113d27c.mjs';\nimport './modifiers-3e8908bb.mjs';\nimport './observable-3cba8a1c.mjs';\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction addAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  if (ids.some(existingId => existingId.trim() === id)) {\n    return;\n  }\n  ids.push(id);\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction removeAriaReferencedId(el, attr, id) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  const filteredIds = ids.filter(val => val !== id);\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nfunction getAriaReferenceIds(el, attr) {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  const attrValue = el.getAttribute(attr);\n  return attrValue?.match(/\\S+/g) ?? [];\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nconst MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nconst CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\nclass AriaDescriber {\n  _platform = inject(Platform);\n  _document = inject(DOCUMENT);\n  /** Map of all registered message elements that have been placed into the document. */\n  _messageRegistry = new Map();\n  /** Container for all registered messages. */\n  _messagesContainer = null;\n  /** Unique ID for the service. */\n  _id = `${nextId++}`;\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    this._id = inject(APP_ID) + '-' + nextId++;\n  }\n  describe(hostElement, message, role) {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message, this._id);\n      this._messageRegistry.set(key, {\n        messageElement: message,\n        referenceCount: 0\n      });\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n  removeDescription(hostElement, message, role) {\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n    const key = getKey(message, role);\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    }\n    // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n    if (this._messagesContainer?.childNodes.length === 0) {\n      this._messagesContainer.remove();\n      this._messagesContainer = null;\n    }\n  }\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements = this._document.querySelectorAll(`[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`);\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n    this._messagesContainer?.remove();\n    this._messagesContainer = null;\n    this._messageRegistry.clear();\n  }\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  _createMessageElement(message, role) {\n    const messageElement = this._document.createElement('div');\n    setMessageId(messageElement, this._id);\n    messageElement.textContent = message;\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n    this._createMessagesContainer();\n    this._messagesContainer.appendChild(messageElement);\n    this._messageRegistry.set(getKey(message, role), {\n      messageElement,\n      referenceCount: 0\n    });\n  }\n  /** Deletes the message element from the global messages container. */\n  _deleteMessageElement(key) {\n    this._messageRegistry.get(key)?.messageElement?.remove();\n    this._messageRegistry.delete(key);\n  }\n  /** Creates the global container for all aria-describedby messages. */\n  _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n    const containerClassName = 'cdk-describedby-message-container';\n    const serverContainers = this._document.querySelectorAll(`.${containerClassName}[platform=\"server\"]`);\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n    const messagesContainer = this._document.createElement('div');\n    // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n    messagesContainer.style.visibility = 'hidden';\n    // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden');\n    if (!this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n    this._document.body.appendChild(messagesContainer);\n    this._messagesContainer = messagesContainer;\n  }\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  _removeCdkDescribedByReferenceIds(element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0);\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  _addMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  _removeMessageReference(element, key) {\n    const registeredMessage = this._messageRegistry.get(key);\n    registeredMessage.referenceCount--;\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n  /** Returns true if the element has been described by the provided message ID. */\n  _isElementDescribedByMessage(element, key) {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = this._messageRegistry.get(key);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n  /** Determines whether a message can be described on a particular element. */\n  _canBeDescribed(element, message) {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label');\n    // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n  /** Checks whether a node is an Element node. */\n  _isElementNode(element) {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n  static ɵfac = function AriaDescriber_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AriaDescriber)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AriaDescriber,\n    factory: AriaDescriber.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AriaDescriber, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message, role) {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element, serviceId) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nclass NoopTreeKeyManager {\n  _isNoopTreeKeyManager = true;\n  // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n  // implementation that does not emit to streams.\n  change = new Subject();\n  destroy() {\n    this.change.complete();\n  }\n  onKeydown() {\n    // noop\n  }\n  getActiveItemIndex() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  getActiveItem() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n  focusItem() {\n    // noop\n  }\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nfunction NOOP_TREE_KEY_MANAGER_FACTORY() {\n  return () => new NoopTreeKeyManager();\n}\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nconst NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: NOOP_TREE_KEY_MANAGER_FACTORY\n};\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nclass ConfigurableFocusTrap extends FocusTrap {\n  _focusTrapManager;\n  _inertStrategy;\n  /** Whether the FocusTrap is enabled. */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n  constructor(_element, _checker, _ngZone, _document, _focusTrapManager, _inertStrategy, config, injector) {\n    super(_element, _checker, _ngZone, _document, config.defer, injector);\n    this._focusTrapManager = _focusTrapManager;\n    this._inertStrategy = _inertStrategy;\n    this._focusTrapManager.register(this);\n  }\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nclass EventListenerFocusTrapInertStrategy {\n  /** Focus event handler. */\n  _listener = null;\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap) {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener, true);\n    }\n    this._listener = e => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener, true);\n    });\n  }\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap) {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener, true);\n    this._listener = null;\n  }\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  _trapFocus(focusTrap, event) {\n    const target = event.target;\n    const focusTrapRoot = focusTrap._element;\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n\n/** The injection token used to specify the inert strategy. */\nconst FOCUS_TRAP_INERT_STRATEGY = new InjectionToken('FOCUS_TRAP_INERT_STRATEGY');\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\nclass FocusTrapManager {\n  // A stack of the FocusTraps on the page. Only the FocusTrap at the\n  // top of the stack is active.\n  _focusTrapStack = [];\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n  register(focusTrap) {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n    let stack = this._focusTrapStack;\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n    stack.push(focusTrap);\n    focusTrap._enable();\n  }\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n  deregister(focusTrap) {\n    focusTrap._disable();\n    const stack = this._focusTrapStack;\n    const i = stack.indexOf(focusTrap);\n    if (i !== -1) {\n      stack.splice(i, 1);\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n  static ɵfac = function FocusTrapManager_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FocusTrapManager)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FocusTrapManager,\n    factory: FocusTrapManager.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapManager, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/** Factory that allows easy instantiation of configurable focus traps. */\nclass ConfigurableFocusTrapFactory {\n  _checker = inject(InteractivityChecker);\n  _ngZone = inject(NgZone);\n  _focusTrapManager = inject(FocusTrapManager);\n  _document = inject(DOCUMENT);\n  _inertStrategy;\n  _injector = inject(Injector);\n  constructor() {\n    const inertStrategy = inject(FOCUS_TRAP_INERT_STRATEGY, {\n      optional: true\n    });\n    // TODO split up the strategies into different modules, similar to DateAdapter.\n    this._inertStrategy = inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n  create(element, config = {\n    defer: false\n  }) {\n    let configObject;\n    if (typeof config === 'boolean') {\n      configObject = {\n        defer: config\n      };\n    } else {\n      configObject = config;\n    }\n    return new ConfigurableFocusTrap(element, this._checker, this._ngZone, this._document, this._focusTrapManager, this._inertStrategy, configObject, this._injector);\n  }\n  static ɵfac = function ConfigurableFocusTrapFactory_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ConfigurableFocusTrapFactory)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ConfigurableFocusTrapFactory,\n    factory: ConfigurableFocusTrapFactory.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfigurableFocusTrapFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { AriaDescriber, CDK_DESCRIBEDBY_HOST_ATTRIBUTE, CDK_DESCRIBEDBY_ID_PREFIX, ConfigurableFocusTrap, ConfigurableFocusTrapFactory, EventListenerFocusTrapInertStrategy, FOCUS_TRAP_INERT_STRATEGY, MESSAGES_CONTAINER_ID, NOOP_TREE_KEY_MANAGER_FACTORY, NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER, NoopTreeKeyManager, addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId };\n", "import { QueryList, InjectionToken } from '@angular/core';\nimport { Subscription, isObservable, Subject, of } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { T as Typeahead } from './typeahead-0113d27c.mjs';\nimport { c as coerceObservable } from './observable-3cba8a1c.mjs';\n\n/**\n * This class manages keyboard events for trees. If you pass it a QueryList or other list of tree\n * items, it will set the active item, focus, handle expansion and typeahead correctly when\n * keyboard events occur.\n */\nclass TreeKeyManager {\n  /** The index of the currently active (focused) item. */\n  _activeItemIndex = -1;\n  /** The currently active (focused) item. */\n  _activeItem = null;\n  /** Whether or not we activate the item when it's focused. */\n  _shouldActivationFollowFocus = false;\n  /**\n   * The orientation that the tree is laid out in. In `rtl` mode, the behavior of Left and\n   * Right arrow are switched.\n   */\n  _horizontalOrientation = 'ltr';\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager.\n   *\n   * The default value for this doesn't skip any elements in order to keep tree items focusable\n   * when disabled. This aligns with ARIA guidelines:\n   * https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#focusabilityofdisabledcontrols.\n   */\n  _skipPredicateFn = _item => false;\n  /** Function to determine equivalent items. */\n  _trackByFn = item => item;\n  /** Synchronous cache of the items to manage. */\n  _items = [];\n  _typeahead;\n  _typeaheadSubscription = Subscription.EMPTY;\n  _hasInitialFocused = false;\n  _initializeFocus() {\n    if (this._hasInitialFocused || this._items.length === 0) {\n      return;\n    }\n    let activeIndex = 0;\n    for (let i = 0; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i]) && !this._isItemDisabled(this._items[i])) {\n        activeIndex = i;\n        break;\n      }\n    }\n    const activeItem = this._items[activeIndex];\n    // Use `makeFocusable` here, because we want the item to just be focusable, not actually\n    // capture the focus since the user isn't interacting with it. See #29628.\n    if (activeItem.makeFocusable) {\n      this._activeItem?.unfocus();\n      this._activeItemIndex = activeIndex;\n      this._activeItem = activeItem;\n      this._typeahead?.setCurrentSelectedItemIndex(activeIndex);\n      activeItem.makeFocusable();\n    } else {\n      // Backwards compatibility for items that don't implement `makeFocusable`.\n      this.focusItem(activeIndex);\n    }\n    this._hasInitialFocused = true;\n  }\n  /**\n   *\n   * @param items List of TreeKeyManager options. Can be synchronous or asynchronous.\n   * @param config Optional configuration options. By default, use 'ltr' horizontal orientation. By\n   * default, do not skip any nodes. By default, key manager only calls `focus` method when items\n   * are focused and does not call `activate`. If `typeaheadDefaultInterval` is `true`, use a\n   * default interval of 200ms.\n   */\n  constructor(items, config) {\n    // We allow for the items to be an array or Observable because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (items instanceof QueryList) {\n      this._items = items.toArray();\n      items.changes.subscribe(newItems => {\n        this._items = newItems.toArray();\n        this._typeahead?.setItems(this._items);\n        this._updateActiveItemIndex(this._items);\n        this._initializeFocus();\n      });\n    } else if (isObservable(items)) {\n      items.subscribe(newItems => {\n        this._items = newItems;\n        this._typeahead?.setItems(newItems);\n        this._updateActiveItemIndex(newItems);\n        this._initializeFocus();\n      });\n    } else {\n      this._items = items;\n      this._initializeFocus();\n    }\n    if (typeof config.shouldActivationFollowFocus === 'boolean') {\n      this._shouldActivationFollowFocus = config.shouldActivationFollowFocus;\n    }\n    if (config.horizontalOrientation) {\n      this._horizontalOrientation = config.horizontalOrientation;\n    }\n    if (config.skipPredicate) {\n      this._skipPredicateFn = config.skipPredicate;\n    }\n    if (config.trackBy) {\n      this._trackByFn = config.trackBy;\n    }\n    if (typeof config.typeAheadDebounceInterval !== 'undefined') {\n      this._setTypeAhead(config.typeAheadDebounceInterval);\n    }\n  }\n  /** Stream that emits any time the focused item changes. */\n  change = new Subject();\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._typeahead?.destroy();\n    this.change.complete();\n  }\n  /**\n   * Handles a keyboard event on the tree.\n   * @param event Keyboard event that represents the user interaction with the tree.\n   */\n  onKeydown(event) {\n    const key = event.key;\n    switch (key) {\n      case 'Tab':\n        // Return early here, in order to allow Tab to actually tab out of the tree\n        return;\n      case 'ArrowDown':\n        this._focusNextItem();\n        break;\n      case 'ArrowUp':\n        this._focusPreviousItem();\n        break;\n      case 'ArrowRight':\n        this._horizontalOrientation === 'rtl' ? this._collapseCurrentItem() : this._expandCurrentItem();\n        break;\n      case 'ArrowLeft':\n        this._horizontalOrientation === 'rtl' ? this._expandCurrentItem() : this._collapseCurrentItem();\n        break;\n      case 'Home':\n        this._focusFirstItem();\n        break;\n      case 'End':\n        this._focusLastItem();\n        break;\n      case 'Enter':\n      case ' ':\n        this._activateCurrentItem();\n        break;\n      default:\n        if (event.key === '*') {\n          this._expandAllItemsAtCurrentItemLevel();\n          break;\n        }\n        this._typeahead?.handleKey(event);\n        // Return here, in order to avoid preventing the default action of non-navigational\n        // keys or resetting the buffer of pressed letters.\n        return;\n    }\n    // Reset the typeahead since the user has used a navigational key.\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n  /** Index of the currently active item. */\n  getActiveItemIndex() {\n    return this._activeItemIndex;\n  }\n  /** The currently active item. */\n  getActiveItem() {\n    return this._activeItem;\n  }\n  /** Focus the first available item. */\n  _focusFirstItem() {\n    this.focusItem(this._findNextAvailableItemIndex(-1));\n  }\n  /** Focus the last available item. */\n  _focusLastItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._items.length));\n  }\n  /** Focus the next available item. */\n  _focusNextItem() {\n    this.focusItem(this._findNextAvailableItemIndex(this._activeItemIndex));\n  }\n  /** Focus the previous available item. */\n  _focusPreviousItem() {\n    this.focusItem(this._findPreviousAvailableItemIndex(this._activeItemIndex));\n  }\n  focusItem(itemOrIndex, options = {}) {\n    // Set default options\n    options.emitChangeEvent ??= true;\n    let index = typeof itemOrIndex === 'number' ? itemOrIndex : this._items.findIndex(item => this._trackByFn(item) === this._trackByFn(itemOrIndex));\n    if (index < 0 || index >= this._items.length) {\n      return;\n    }\n    const activeItem = this._items[index];\n    // If we're just setting the same item, don't re-call activate or focus\n    if (this._activeItem !== null && this._trackByFn(activeItem) === this._trackByFn(this._activeItem)) {\n      return;\n    }\n    const previousActiveItem = this._activeItem;\n    this._activeItem = activeItem ?? null;\n    this._activeItemIndex = index;\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n    this._activeItem?.focus();\n    previousActiveItem?.unfocus();\n    if (options.emitChangeEvent) {\n      this.change.next(this._activeItem);\n    }\n    if (this._shouldActivationFollowFocus) {\n      this._activateCurrentItem();\n    }\n  }\n  _updateActiveItemIndex(newItems) {\n    const activeItem = this._activeItem;\n    if (!activeItem) {\n      return;\n    }\n    const newIndex = newItems.findIndex(item => this._trackByFn(item) === this._trackByFn(activeItem));\n    if (newIndex > -1 && newIndex !== this._activeItemIndex) {\n      this._activeItemIndex = newIndex;\n      this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n    }\n  }\n  _setTypeAhead(debounceInterval) {\n    this._typeahead = new Typeahead(this._items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item)\n    });\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.focusItem(item);\n    });\n  }\n  _findNextAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex + 1; i < this._items.length; i++) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  _findPreviousAvailableItemIndex(startingIndex) {\n    for (let i = startingIndex - 1; i >= 0; i--) {\n      if (!this._skipPredicateFn(this._items[i])) {\n        return i;\n      }\n    }\n    return startingIndex;\n  }\n  /**\n   * If the item is already expanded, we collapse the item. Otherwise, we will focus the parent.\n   */\n  _collapseCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (this._isCurrentItemExpanded()) {\n      this._activeItem.collapse();\n    } else {\n      const parent = this._activeItem.getParent();\n      if (!parent || this._skipPredicateFn(parent)) {\n        return;\n      }\n      this.focusItem(parent);\n    }\n  }\n  /**\n   * If the item is already collapsed, we expand the item. Otherwise, we will focus the first child.\n   */\n  _expandCurrentItem() {\n    if (!this._activeItem) {\n      return;\n    }\n    if (!this._isCurrentItemExpanded()) {\n      this._activeItem.expand();\n    } else {\n      coerceObservable(this._activeItem.getChildren()).pipe(take(1)).subscribe(children => {\n        const firstChild = children.find(child => !this._skipPredicateFn(child));\n        if (!firstChild) {\n          return;\n        }\n        this.focusItem(firstChild);\n      });\n    }\n  }\n  _isCurrentItemExpanded() {\n    if (!this._activeItem) {\n      return false;\n    }\n    return typeof this._activeItem.isExpanded === 'boolean' ? this._activeItem.isExpanded : this._activeItem.isExpanded();\n  }\n  _isItemDisabled(item) {\n    return typeof item.isDisabled === 'boolean' ? item.isDisabled : item.isDisabled?.();\n  }\n  /** For all items that are the same level as the current item, we expand those items. */\n  _expandAllItemsAtCurrentItemLevel() {\n    if (!this._activeItem) {\n      return;\n    }\n    const parent = this._activeItem.getParent();\n    let itemsToExpand;\n    if (!parent) {\n      itemsToExpand = of(this._items.filter(item => item.getParent() === null));\n    } else {\n      itemsToExpand = coerceObservable(parent.getChildren());\n    }\n    itemsToExpand.pipe(take(1)).subscribe(items => {\n      for (const item of items) {\n        item.expand();\n      }\n    });\n  }\n  _activateCurrentItem() {\n    this._activeItem?.activate();\n  }\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction TREE_KEY_MANAGER_FACTORY() {\n  return (items, options) => new TreeKeyManager(items, options);\n}\n/** Injection token that determines the key manager to use. */\nconst TREE_KEY_MANAGER = new InjectionToken('tree-key-manager', {\n  providedIn: 'root',\n  factory: TREE_KEY_MANAGER_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst TREE_KEY_MANAGER_FACTORY_PROVIDER = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: TREE_KEY_MANAGER_FACTORY\n};\nexport { TREE_KEY_MANAGER as T, TreeKeyManager as a, TREE_KEY_MANAGER_FACTORY as b, TREE_KEY_MANAGER_FACTORY_PROVIDER as c };\n", "import { isObservable, of } from 'rxjs';\n\n/**\n * Given either an Observable or non-Observable value, returns either the original\n * Observable, or wraps it in an Observable that emits the non-Observable value.\n */\nfunction coerceObservable(data) {\n  if (!isObservable(data)) {\n    return of(data);\n  }\n  return data;\n}\nexport { coerceObservable as c };\n", "import { HighContrastModeDetector } from '@angular/cdk/a11y';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, NgModule } from '@angular/core';\n\n/**\n * Injection token that configures whether the Material sanity checks are enabled.\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nconst MATERIAL_SANITY_CHECKS = new InjectionToken('mat-sanity-checks', {\n  providedIn: 'root',\n  factory: () => true\n});\n/**\n * Module that captures anything that should be loaded and/or run for *all* Angular Material\n * components. This includes Bidi, etc.\n *\n * This module should be imported to each top-level component module (e.g., MatTabsModule).\n * @deprecated No longer used and will be removed.\n * @breaking-change 21.0.0\n */\nclass MatCommonModule {\n  constructor() {\n    // While A11yModule also does this, we repeat it here to avoid importing A11yModule\n    // in MatCommonModule.\n    inject(HighContrastModeDetector)._applyBodyHighContrastModeCssClasses();\n  }\n  static ɵfac = function MatCommonModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatCommonModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatCommonModule,\n    imports: [BidiModule],\n    exports: [BidiModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [BidiModule, BidiModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCommonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule],\n      exports: [BidiModule]\n    }]\n  }], () => [], null);\n})();\nexport { MatCommonModule as M, MATERIAL_SANITY_CHECKS as a };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAM,YAAY;AAClB,IAAM,MAAM;AAEZ,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,MAAM;AAGZ,IAAM,SAAS;AACf,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,YAAY;AAClB,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,aAAa;AACnB,IAAM,WAAW;AACjB,IAAM,cAAc;AACpB,IAAM,aAAa;AAKnB,IAAM,OAAO;AASb,IAAM,OAAO;AAKb,IAAM,IAAI;AAyBV,IAAM,IAAI;AACV,IAAM,OAAO;AAsDb,IAAM,WAAW;;;ACtHjB,IAAI;AAEJ,SAAS,qBAAqB;AAC5B,MAAI,wBAAwB,MAAM;AAChC,UAAM,OAAO,OAAO,aAAa,cAAc,SAAS,OAAO;AAC/D,2BAAuB,CAAC,EAAE,SAAS,KAAK,oBAAoB,KAAK;AAAA,EACnE;AACA,SAAO;AACT;AAEA,SAAS,eAAe,SAAS;AAC/B,MAAI,mBAAmB,GAAG;AACxB,UAAM,WAAW,QAAQ,cAAc,QAAQ,YAAY,IAAI;AAG/D,QAAI,OAAO,eAAe,eAAe,cAAc,oBAAoB,YAAY;AACrF,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAKA,SAAS,oCAAoC;AAC3C,MAAI,gBAAgB,OAAO,aAAa,eAAe,WAAW,SAAS,gBAAgB;AAC3F,SAAO,iBAAiB,cAAc,YAAY;AAChD,UAAM,mBAAmB,cAAc,WAAW;AAClD,QAAI,qBAAqB,eAAe;AACtC;AAAA,IACF,OAAO;AACL,sBAAgB;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,OAAO;AAG9B,SAAO,MAAM,eAAe,MAAM,aAAa,EAAE,CAAC,IAAI,MAAM;AAC9D;;;ACxCA,kBAA6C;AAC7C,uBAAsD;;;ACFtD,SAAS,gCAAgC,OAAO;AAM9C,SAAO,MAAM,YAAY,KAAK,MAAM,WAAW;AACjD;AAEA,SAAS,iCAAiC,OAAO;AAC/C,QAAM,QAAQ,MAAM,WAAW,MAAM,QAAQ,CAAC,KAAK,MAAM,kBAAkB,MAAM,eAAe,CAAC;AAKjG,SAAO,CAAC,CAAC,SAAS,MAAM,eAAe,OAAO,MAAM,WAAW,QAAQ,MAAM,YAAY,OAAO,MAAM,WAAW,QAAQ,MAAM,YAAY;AAC7I;;;AChBA,IAAI;AAKJ,SAAS,gCAAgC;AACvC,MAAI,yBAAyB,QAAQ,OAAO,WAAW,aAAa;AAClE,QAAI;AACF,aAAO,iBAAiB,QAAQ,MAAM,OAAO,eAAe,CAAC,GAAG,WAAW;AAAA,QACzE,KAAK,MAAM,wBAAwB;AAAA,MACrC,CAAC,CAAC;AAAA,IACJ,UAAE;AACA,8BAAwB,yBAAyB;AAAA,IACnD;AAAA,EACF;AACA,SAAO;AACT;AAOA,SAAS,gCAAgC,SAAS;AAChD,SAAO,8BAA8B,IAAI,UAAU,CAAC,CAAC,QAAQ;AAC/D;;;AFTA,IAAM,kCAAkC,IAAI,eAAe,qCAAqC;AAiBhG,IAAM,0CAA0C;AAAA,EAC9C,YAAY,CAAC,KAAK,SAAS,UAAU,MAAM,KAAK;AAClD;AAQA,IAAM,kBAAkB;AAKxB,IAAM,+BAA+B;AAAA,EACnC,SAAS;AAAA,EACT,SAAS;AACX;AAeA,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,IAAI,qBAAqB;AACvB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAAA;AAAA,EAEpB,YAAY,IAAI,4BAAgB,IAAI;AAAA;AAAA,EAEpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,aAAa,WAAS;AAGpB,QAAI,KAAK,UAAU,YAAY,KAAK,aAAW,YAAY,MAAM,OAAO,GAAG;AACzE;AAAA,IACF;AACA,SAAK,UAAU,KAAK,UAAU;AAC9B,SAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,WAAS;AAItB,QAAI,KAAK,IAAI,IAAI,KAAK,eAAe,iBAAiB;AACpD;AAAA,IACF;AAGA,SAAK,UAAU,KAAK,gCAAgC,KAAK,IAAI,aAAa,OAAO;AACjF,SAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,WAAS;AAGvB,QAAI,iCAAiC,KAAK,GAAG;AAC3C,WAAK,UAAU,KAAK,UAAU;AAC9B;AAAA,IACF;AAGA,SAAK,eAAe,KAAK,IAAI;AAC7B,SAAK,UAAU,KAAK,OAAO;AAC3B,SAAK,oBAAoB,gBAAgB,KAAK;AAAA,EAChD;AAAA,EACA,cAAc;AACZ,UAAM,SAAS,OAAO,MAAM;AAC5B,UAAMA,YAAW,OAAO,QAAQ;AAChC,UAAM,UAAU,OAAO,iCAAiC;AAAA,MACtD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,WAAW,kCACX,0CACA;AAGL,SAAK,mBAAmB,KAAK,UAAU,SAAK,uBAAK,CAAC,CAAC;AACnD,SAAK,kBAAkB,KAAK,iBAAiB,SAAK,uCAAqB,CAAC;AAGxE,QAAI,KAAK,UAAU,WAAW;AAC5B,YAAM,WAAW,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AACnE,WAAK,oBAAoB,OAAO,kBAAkB,MAAM;AACtD,eAAO,CAAC,sBAAsB,UAAUA,WAAU,WAAW,KAAK,YAAY,4BAA4B,GAAG,sBAAsB,UAAUA,WAAU,aAAa,KAAK,cAAc,4BAA4B,GAAG,sBAAsB,UAAUA,WAAU,cAAc,KAAK,eAAe,4BAA4B,CAAC;AAAA,MACjU,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,UAAU,SAAS;AACxB,SAAK,mBAAmB,QAAQ,aAAW,QAAQ,CAAC;AAAA,EACtD;AAAA,EACA,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,uBAAsB;AAAA,IAC/B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAGH,IAAI;AAAA,CACH,SAAUC,4BAA2B;AAMpC,EAAAA,2BAA0BA,2BAA0B,WAAW,IAAI,CAAC,IAAI;AAKxE,EAAAA,2BAA0BA,2BAA0B,UAAU,IAAI,CAAC,IAAI;AACzE,GAAG,8BAA8B,4BAA4B,CAAC,EAAE;AAEhE,IAAM,gCAAgC,IAAI,eAAe,mCAAmC;AAK5F,IAAM,8BAA8B,gCAAgC;AAAA,EAClE,SAAS;AAAA,EACT,SAAS;AACX,CAAC;AAED,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,yBAAyB,OAAO,qBAAqB;AAAA;AAAA,EAErD,UAAU;AAAA;AAAA,EAEV;AAAA;AAAA,EAEA,iBAAiB;AAAA;AAAA,EAEjB;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,8BAA8B;AAAA;AAAA,EAE9B,eAAe,oBAAI,IAAI;AAAA;AAAA,EAEvB,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOzB,8BAA8B,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,MAAM;AAG3B,SAAK,iBAAiB;AACtB,SAAK,wBAAwB,WAAW,MAAM,KAAK,iBAAiB,KAAK;AAAA,EAC3E;AAAA;AAAA,EAEA,YAAY,OAAO,UAAU;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED,6BAA6B,IAAI,oBAAQ;AAAA,EACzC,cAAc;AACZ,UAAM,UAAU,OAAO,+BAA+B;AAAA,MACpD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,iBAAiB,SAAS,iBAAiB,0BAA0B;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gCAAgC,WAAS;AACvC,UAAM,SAAS,gBAAgB,KAAK;AAEpC,aAAS,UAAU,QAAQ,SAAS,UAAU,QAAQ,eAAe;AACnE,UAAI,MAAM,SAAS,SAAS;AAC1B,aAAK,SAAS,OAAO,OAAO;AAAA,MAC9B,OAAO;AACL,aAAK,QAAQ,OAAO,OAAO;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,gBAAgB,OAAO;AACtC,UAAM,gBAAgB,cAAc,OAAO;AAE3C,QAAI,CAAC,KAAK,UAAU,aAAa,cAAc,aAAa,GAAG;AAE7D,iBAAO,gBAAG;AAAA,IACZ;AAIA,UAAM,WAAW,eAAe,aAAa,KAAK,KAAK,aAAa;AACpE,UAAM,aAAa,KAAK,aAAa,IAAI,aAAa;AAEtD,QAAI,YAAY;AACd,UAAI,eAAe;AAIjB,mBAAW,gBAAgB;AAAA,MAC7B;AACA,aAAO,WAAW;AAAA,IACpB;AAEA,UAAM,OAAO;AAAA,MACX;AAAA,MACA,SAAS,IAAI,oBAAQ;AAAA,MACrB;AAAA,IACF;AACA,SAAK,aAAa,IAAI,eAAe,IAAI;AACzC,SAAK,yBAAyB,IAAI;AAClC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe,SAAS;AACtB,UAAM,gBAAgB,cAAc,OAAO;AAC3C,UAAM,cAAc,KAAK,aAAa,IAAI,aAAa;AACvD,QAAI,aAAa;AACf,kBAAY,QAAQ,SAAS;AAC7B,WAAK,YAAY,aAAa;AAC9B,WAAK,aAAa,OAAO,aAAa;AACtC,WAAK,uBAAuB,WAAW;AAAA,IACzC;AAAA,EACF;AAAA,EACA,SAAS,SAAS,QAAQ,SAAS;AACjC,UAAM,gBAAgB,cAAc,OAAO;AAC3C,UAAM,iBAAiB,KAAK,aAAa,EAAE;AAI3C,QAAI,kBAAkB,gBAAgB;AACpC,WAAK,wBAAwB,aAAa,EAAE,QAAQ,CAAC,CAAC,gBAAgB,IAAI,MAAM,KAAK,eAAe,gBAAgB,QAAQ,IAAI,CAAC;AAAA,IACnI,OAAO;AACL,WAAK,WAAW,MAAM;AAEtB,UAAI,OAAO,cAAc,UAAU,YAAY;AAC7C,sBAAc,MAAM,OAAO;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,QAAQ,CAAC,OAAO,YAAY,KAAK,eAAe,OAAO,CAAC;AAAA,EAC5E;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa;AACX,UAAM,MAAM,KAAK,aAAa;AAC9B,WAAO,IAAI,eAAe;AAAA,EAC5B;AAAA,EACA,gBAAgB,kBAAkB;AAChC,QAAI,KAAK,SAAS;AAGhB,UAAI,KAAK,6BAA6B;AACpC,eAAO,KAAK,2BAA2B,gBAAgB,IAAI,UAAU;AAAA,MACvE,OAAO;AACL,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAUA,QAAI,KAAK,kBAAkB,KAAK,kBAAkB;AAChD,aAAO,KAAK;AAAA,IACd;AAKA,QAAI,oBAAoB,KAAK,iCAAiC,gBAAgB,GAAG;AAC/E,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,2BAA2B,kBAAkB;AAW3C,WAAO,KAAK,mBAAmB,0BAA0B,YAAY,CAAC,CAAC,kBAAkB,SAAS,KAAK,uBAAuB,iBAAiB;AAAA,EACjJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAS,QAAQ;AAC3B,YAAQ,UAAU,OAAO,eAAe,CAAC,CAAC,MAAM;AAChD,YAAQ,UAAU,OAAO,qBAAqB,WAAW,OAAO;AAChE,YAAQ,UAAU,OAAO,wBAAwB,WAAW,UAAU;AACtE,YAAQ,UAAU,OAAO,qBAAqB,WAAW,OAAO;AAChE,YAAQ,UAAU,OAAO,uBAAuB,WAAW,SAAS;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,QAAQ,oBAAoB,OAAO;AAC5C,SAAK,QAAQ,kBAAkB,MAAM;AACnC,WAAK,UAAU;AACf,WAAK,8BAA8B,WAAW,WAAW;AAMzD,UAAI,KAAK,mBAAmB,0BAA0B,WAAW;AAC/D,qBAAa,KAAK,gBAAgB;AAClC,cAAM,KAAK,KAAK,8BAA8B,kBAAkB;AAChE,aAAK,mBAAmB,WAAW,MAAM,KAAK,UAAU,MAAM,EAAE;AAAA,MAClE;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO,SAAS;AAOvB,UAAM,cAAc,KAAK,aAAa,IAAI,OAAO;AACjD,UAAM,mBAAmB,gBAAgB,KAAK;AAC9C,QAAI,CAAC,eAAe,CAAC,YAAY,iBAAiB,YAAY,kBAAkB;AAC9E;AAAA,IACF;AACA,SAAK,eAAe,SAAS,KAAK,gBAAgB,gBAAgB,GAAG,WAAW;AAAA,EAClF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,OAAO,SAAS;AAGtB,UAAM,cAAc,KAAK,aAAa,IAAI,OAAO;AACjD,QAAI,CAAC,eAAe,YAAY,iBAAiB,MAAM,yBAAyB,QAAQ,QAAQ,SAAS,MAAM,aAAa,GAAG;AAC7H;AAAA,IACF;AACA,SAAK,YAAY,OAAO;AACxB,SAAK,YAAY,aAAa,IAAI;AAAA,EACpC;AAAA,EACA,YAAY,MAAM,QAAQ;AACxB,QAAI,KAAK,QAAQ,UAAU,QAAQ;AACjC,WAAK,QAAQ,IAAI,MAAM,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,IAClD;AAAA,EACF;AAAA,EACA,yBAAyB,aAAa;AACpC,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B;AAAA,IACF;AACA,UAAM,WAAW,YAAY;AAC7B,UAAM,yBAAyB,KAAK,4BAA4B,IAAI,QAAQ,KAAK;AACjF,QAAI,CAAC,wBAAwB;AAC3B,WAAK,QAAQ,kBAAkB,MAAM;AACnC,iBAAS,iBAAiB,SAAS,KAAK,+BAA+B,2BAA2B;AAClG,iBAAS,iBAAiB,QAAQ,KAAK,+BAA+B,2BAA2B;AAAA,MACnG,CAAC;AAAA,IACH;AACA,SAAK,4BAA4B,IAAI,UAAU,yBAAyB,CAAC;AAEzE,QAAI,EAAE,KAAK,2BAA2B,GAAG;AAGvC,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAMC,UAAS,KAAK,WAAW;AAC/B,QAAAA,QAAO,iBAAiB,SAAS,KAAK,oBAAoB;AAAA,MAC5D,CAAC;AAED,WAAK,uBAAuB,iBAAiB,SAAK,4BAAU,KAAK,0BAA0B,CAAC,EAAE,UAAU,cAAY;AAClH,aAAK;AAAA,UAAW;AAAA,UAAU;AAAA;AAAA,QAA4B;AAAA,MACxD,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,uBAAuB,aAAa;AAClC,UAAM,WAAW,YAAY;AAC7B,QAAI,KAAK,4BAA4B,IAAI,QAAQ,GAAG;AAClD,YAAM,yBAAyB,KAAK,4BAA4B,IAAI,QAAQ;AAC5E,UAAI,yBAAyB,GAAG;AAC9B,aAAK,4BAA4B,IAAI,UAAU,yBAAyB,CAAC;AAAA,MAC3E,OAAO;AACL,iBAAS,oBAAoB,SAAS,KAAK,+BAA+B,2BAA2B;AACrG,iBAAS,oBAAoB,QAAQ,KAAK,+BAA+B,2BAA2B;AACpG,aAAK,4BAA4B,OAAO,QAAQ;AAAA,MAClD;AAAA,IACF;AAEA,QAAI,CAAE,EAAE,KAAK,wBAAwB;AACnC,YAAMA,UAAS,KAAK,WAAW;AAC/B,MAAAA,QAAO,oBAAoB,SAAS,KAAK,oBAAoB;AAE7D,WAAK,2BAA2B,KAAK;AAErC,mBAAa,KAAK,qBAAqB;AACvC,mBAAa,KAAK,gBAAgB;AAAA,IACpC;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,SAAS,QAAQ,aAAa;AAC3C,SAAK,YAAY,SAAS,MAAM;AAChC,SAAK,YAAY,aAAa,MAAM;AACpC,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,SAAS;AAC/B,UAAM,UAAU,CAAC;AACjB,SAAK,aAAa,QAAQ,CAAC,MAAM,mBAAmB;AAClD,UAAI,mBAAmB,WAAW,KAAK,iBAAiB,eAAe,SAAS,OAAO,GAAG;AACxF,gBAAQ,KAAK,CAAC,gBAAgB,IAAI,CAAC;AAAA,MACrC;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iCAAiC,kBAAkB;AACjD,UAAM;AAAA,MACJ,mBAAmB;AAAA,MACnB;AAAA,IACF,IAAI,KAAK;AAIT,QAAI,uBAAuB,WAAW,CAAC,oBAAoB,qBAAqB,oBAAoB,iBAAiB,aAAa,WAAW,iBAAiB,aAAa,cAAc,iBAAiB,UAAU;AAClN,aAAO;AAAA,IACT;AACA,UAAM,SAAS,iBAAiB;AAChC,QAAI,QAAQ;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,OAAO,CAAC,EAAE,SAAS,gBAAgB,GAAG;AACxC,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAUH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc,OAAO,UAAU;AAAA,EAC/B,gBAAgB,OAAO,YAAY;AAAA,EACnC;AAAA,EACA,eAAe;AAAA,EACf,iBAAiB,IAAI,aAAa;AAAA,EAClC,cAAc;AAAA,EAAC;AAAA,EACf,IAAI,cAAc;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,SAAK,uBAAuB,KAAK,cAAc,QAAQ,SAAS,QAAQ,aAAa,KAAK,QAAQ,aAAa,wBAAwB,CAAC,EAAE,UAAU,YAAU;AAC5J,WAAK,eAAe;AACpB,WAAK,eAAe,KAAK,MAAM;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,eAAe,KAAK,WAAW;AAClD,QAAI,KAAK,sBAAsB;AAC7B,WAAK,qBAAqB,YAAY;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,0BAA0B,EAAE,GAAG,CAAC,IAAI,0BAA0B,EAAE,CAAC;AAAA,IAClF,SAAS;AAAA,MACP,gBAAgB;AAAA,IAClB;AAAA,IACA,UAAU,CAAC,iBAAiB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;;;AG9nBH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO,OAAO,SAAS,8BAA8B,mBAAmB;AACtE,WAAO,KAAK,qBAAqB,wBAAuB;AAAA,EAC1D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,UAAU,CAAC,mBAAmB;AAAA,IAC9B,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AAAA,IAAC;AAAA,IAC5D,QAAQ,CAAC,kQAAkQ;AAAA,IAC3Q,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,kQAAkQ;AAAA,IAC7Q,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AChCH,IAAAC,eAA2D;AAC3D,IAAAC,oBAAoE;;;ACHpE,SAAS,YAAY,OAAO;AAC1B,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;;;ADMA,IAAM,qCAAqC,oBAAI,IAAI;AAEnD,IAAI;AAEJ,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,YAAY,OAAO,QAAQ;AAAA,EAC3B,SAAS,OAAO,WAAW;AAAA,IACzB,UAAU;AAAA,EACZ,CAAC;AAAA;AAAA,EAED;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,KAAK,UAAU,aAAa,OAAO;AAAA;AAAA;AAAA,MAGtD,OAAO,WAAW,KAAK,MAAM;AAAA,QAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,OAAO;AAChB,QAAI,KAAK,UAAU,UAAU,KAAK,UAAU,OAAO;AACjD,2BAAqB,OAAO,KAAK,MAAM;AAAA,IACzC;AACA,WAAO,KAAK,YAAY,KAAK;AAAA,EAC/B;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAUH,SAAS,qBAAqB,OAAO,OAAO;AAC1C,MAAI,mCAAmC,IAAI,KAAK,GAAG;AACjD;AAAA,EACF;AACA,MAAI;AACF,QAAI,CAAC,qBAAqB;AACxB,4BAAsB,SAAS,cAAc,OAAO;AACpD,UAAI,OAAO;AACT,4BAAoB,aAAa,SAAS,KAAK;AAAA,MACjD;AACA,0BAAoB,aAAa,QAAQ,UAAU;AACnD,eAAS,KAAK,YAAY,mBAAmB;AAAA,IAC/C;AACA,QAAI,oBAAoB,OAAO;AAC7B,0BAAoB,MAAM,WAAW,UAAU,KAAK,cAAc,CAAC;AACnE,yCAAmC,IAAI,KAAK;AAAA,IAC9C;AAAA,EACF,SAAS,GAAG;AACV,YAAQ,MAAM,CAAC;AAAA,EACjB;AACF;AAEA,SAAS,eAAe,OAAO;AAG7B,SAAO;AAAA,IACL,SAAS,UAAU,SAAS,UAAU;AAAA,IACtC,OAAO;AAAA,IACP,aAAa,MAAM;AAAA,IAAC;AAAA,IACpB,gBAAgB,MAAM;AAAA,IAAC;AAAA,EACzB;AACF;AAGA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,gBAAgB,OAAO,YAAY;AAAA,EACnC,QAAQ,OAAO,MAAM;AAAA;AAAA,EAErB,WAAW,oBAAI,IAAI;AAAA;AAAA,EAEnB,kBAAkB,IAAI,qBAAQ;AAAA,EAC9B,cAAc;AAAA,EAAC;AAAA;AAAA,EAEf,cAAc;AACZ,SAAK,gBAAgB,KAAK;AAC1B,SAAK,gBAAgB,SAAS;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,OAAO;AACf,UAAM,UAAU,aAAa,YAAY,KAAK,CAAC;AAC/C,WAAO,QAAQ,KAAK,gBAAc,KAAK,eAAe,UAAU,EAAE,IAAI,OAAO;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,OAAO;AACb,UAAM,UAAU,aAAa,YAAY,KAAK,CAAC;AAC/C,UAAM,cAAc,QAAQ,IAAI,WAAS,KAAK,eAAe,KAAK,EAAE,UAAU;AAC9E,QAAI,sBAAkB,4BAAc,WAAW;AAE/C,0BAAkB,qBAAO,gBAAgB,SAAK,wBAAK,CAAC,CAAC,GAAG,gBAAgB,SAAK,wBAAK,CAAC,OAAG,gCAAa,CAAC,CAAC,CAAC;AACtG,WAAO,gBAAgB,SAAK,uBAAI,sBAAoB;AAClD,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,QACT,aAAa,CAAC;AAAA,MAChB;AACA,uBAAiB,QAAQ,CAAC;AAAA,QACxB;AAAA,QACA;AAAA,MACF,MAAM;AACJ,iBAAS,UAAU,SAAS,WAAW;AACvC,iBAAS,YAAY,KAAK,IAAI;AAAA,MAChC,CAAC;AACD,aAAO;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA,EAEA,eAAe,OAAO;AAEpB,QAAI,KAAK,SAAS,IAAI,KAAK,GAAG;AAC5B,aAAO,KAAK,SAAS,IAAI,KAAK;AAAA,IAChC;AACA,UAAM,MAAM,KAAK,cAAc,WAAW,KAAK;AAE/C,UAAM,kBAAkB,IAAI,wBAAW,cAAY;AAMjD,YAAM,UAAU,OAAK,KAAK,MAAM,IAAI,MAAM,SAAS,KAAK,CAAC,CAAC;AAC1D,UAAI,YAAY,OAAO;AACvB,aAAO,MAAM;AACX,YAAI,eAAe,OAAO;AAAA,MAC5B;AAAA,IACF,CAAC,EAAE,SAAK,6BAAU,GAAG,OAAG,uBAAI,CAAC;AAAA,MAC3B;AAAA,IACF,OAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF,EAAE,OAAG,6BAAU,KAAK,eAAe,CAAC;AAEpC,UAAM,SAAS;AAAA,MACb,YAAY;AAAA,MACZ;AAAA,IACF;AACA,SAAK,SAAS,IAAI,OAAO,MAAM;AAC/B,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,oBAAmB;AAAA,IAC5B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,SAAS,aAAa,SAAS;AAC7B,SAAO,QAAQ,IAAI,WAAS,MAAM,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,IAAI,OAAO,GAAG,OAAO,EAAE,CAAC,EAAE,IAAI,WAAS,MAAM,KAAK,CAAC;AAC3G;;;AExMA,IAAAC,eAAoC;AACpC,IAAAC,oBAA0C;AAO1C,SAAS,mBAAmB,QAAQ;AAElC,MAAI,OAAO,SAAS,mBAAmB,OAAO,kBAAkB,SAAS;AACvE,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,aAAa;AAC/B,aAAS,IAAI,GAAG,IAAI,OAAO,WAAW,QAAQ,KAAK;AACjD,UAAI,EAAE,OAAO,WAAW,CAAC,aAAa,UAAU;AAC9C,eAAO;AAAA,MACT;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,aAAa,QAAQ,KAAK;AACnD,UAAI,EAAE,OAAO,aAAa,CAAC,aAAa,UAAU;AAChD,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAKA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,UAAU;AACf,WAAO,OAAO,qBAAqB,cAAc,OAAO,IAAI,iBAAiB,QAAQ;AAAA,EACvF;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,yBAAwB;AAAA,IACjC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,2BAA2B,OAAO,uBAAuB;AAAA;AAAA,EAEzD,oBAAoB,oBAAI,IAAI;AAAA,EAC5B,UAAU,OAAO,MAAM;AAAA,EACvB,cAAc;AAAA,EAAC;AAAA,EACf,cAAc;AACZ,SAAK,kBAAkB,QAAQ,CAAC,GAAG,YAAY,KAAK,iBAAiB,OAAO,CAAC;AAAA,EAC/E;AAAA,EACA,QAAQ,cAAc;AACpB,UAAM,UAAU,cAAc,YAAY;AAC1C,WAAO,IAAI,wBAAW,cAAY;AAChC,YAAM,SAAS,KAAK,gBAAgB,OAAO;AAC3C,YAAM,eAAe,OAAO,SAAK,uBAAI,aAAW,QAAQ,OAAO,YAAU,CAAC,mBAAmB,MAAM,CAAC,CAAC,OAAG,0BAAO,aAAW,CAAC,CAAC,QAAQ,MAAM,CAAC,EAAE,UAAU,aAAW;AAChK,aAAK,QAAQ,IAAI,MAAM;AACrB,mBAAS,KAAK,OAAO;AAAA,QACvB,CAAC;AAAA,MACH,CAAC;AACD,aAAO,MAAM;AACX,qBAAa,YAAY;AACzB,aAAK,kBAAkB,OAAO;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,SAAS;AACvB,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,UAAI,CAAC,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACxC,cAAM,SAAS,IAAI,qBAAQ;AAC3B,cAAM,WAAW,KAAK,yBAAyB,OAAO,eAAa,OAAO,KAAK,SAAS,CAAC;AACzF,YAAI,UAAU;AACZ,mBAAS,QAAQ,SAAS;AAAA,YACxB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AACA,aAAK,kBAAkB,IAAI,SAAS;AAAA,UAClC;AAAA,UACA;AAAA,UACA,OAAO;AAAA,QACT,CAAC;AAAA,MACH,OAAO;AACL,aAAK,kBAAkB,IAAI,OAAO,EAAE;AAAA,MACtC;AACA,aAAO,KAAK,kBAAkB,IAAI,OAAO,EAAE;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,SAAS;AACzB,QAAI,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACvC,WAAK,kBAAkB,IAAI,OAAO,EAAE;AACpC,UAAI,CAAC,KAAK,kBAAkB,IAAI,OAAO,EAAE,OAAO;AAC9C,aAAK,iBAAiB,OAAO;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,SAAS;AACxB,QAAI,KAAK,kBAAkB,IAAI,OAAO,GAAG;AACvC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,kBAAkB,IAAI,OAAO;AACtC,UAAI,UAAU;AACZ,iBAAS,WAAW;AAAA,MACtB;AACA,aAAO,SAAS;AAChB,WAAK,kBAAkB,OAAO,OAAO;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,mBAAmB,OAAO,eAAe;AAAA,EACzC,cAAc,OAAO,UAAU;AAAA;AAAA,EAE/B,QAAQ,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,YAAY,KAAK,aAAa,IAAI,KAAK,WAAW;AAAA,EACzD;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,qBAAqB,KAAK;AAC3C,SAAK,WAAW;AAAA,EAClB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,cAAc;AAAA,EAAC;AAAA,EACf,qBAAqB;AACnB,QAAI,CAAC,KAAK,wBAAwB,CAAC,KAAK,UAAU;AAChD,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,aAAa;AACX,SAAK,aAAa;AAClB,UAAM,SAAS,KAAK,iBAAiB,QAAQ,KAAK,WAAW;AAC7D,SAAK,wBAAwB,KAAK,WAAW,OAAO,SAAK,gCAAa,KAAK,QAAQ,CAAC,IAAI,QAAQ,UAAU,KAAK,KAAK;AAAA,EACtH;AAAA,EACA,eAAe;AACb,SAAK,sBAAsB,YAAY;AAAA,EACzC;AAAA,EACA,OAAO,OAAO,SAAS,0BAA0B,mBAAmB;AAClE,WAAO,KAAK,qBAAqB,oBAAmB;AAAA,EACtD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,IACzC,QAAQ;AAAA,MACN,UAAU,CAAC,GAAG,6BAA6B,YAAY,gBAAgB;AAAA,MACvE,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,OAAO;AAAA,IACT;AAAA,IACA,UAAU,CAAC,mBAAmB;AAAA,EAChC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB;AAAA,IAC3B,SAAS,CAAC,iBAAiB;AAAA,EAC7B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,uBAAuB;AAAA,EACrC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB;AAAA,MAC3B,SAAS,CAAC,iBAAiB;AAAA,MAC3B,WAAW,CAAC,uBAAuB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC3OH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOf,WAAW,SAAS;AAGlB,WAAO,QAAQ,aAAa,UAAU;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,SAAS;AACjB,WAAO,YAAY,OAAO,KAAK,iBAAiB,OAAO,EAAE,eAAe;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,SAAS;AAElB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO;AAAA,IACT;AACA,UAAM,eAAe,gBAAgB,UAAU,OAAO,CAAC;AACvD,QAAI,cAAc;AAEhB,UAAI,iBAAiB,YAAY,MAAM,IAAI;AACzC,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,KAAK,UAAU,YAAY,GAAG;AACjC,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,QAAI,gBAAgB,iBAAiB,OAAO;AAC5C,QAAI,QAAQ,aAAa,iBAAiB,GAAG;AAC3C,aAAO,kBAAkB;AAAA,IAC3B;AACA,QAAI,aAAa,YAAY,aAAa,UAAU;AAIlD,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,UAAU,UAAU,KAAK,UAAU,OAAO,CAAC,yBAAyB,OAAO,GAAG;AACrF,aAAO;AAAA,IACT;AACA,QAAI,aAAa,SAAS;AAGxB,UAAI,CAAC,QAAQ,aAAa,UAAU,GAAG;AACrC,eAAO;AAAA,MACT;AAGA,aAAO,kBAAkB;AAAA,IAC3B;AACA,QAAI,aAAa,SAAS;AAKxB,UAAI,kBAAkB,IAAI;AACxB,eAAO;AAAA,MACT;AAGA,UAAI,kBAAkB,MAAM;AAC1B,eAAO;AAAA,MACT;AAIA,aAAO,KAAK,UAAU,WAAW,QAAQ,aAAa,UAAU;AAAA,IAClE;AACA,WAAO,QAAQ,YAAY;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,SAAS,QAAQ;AAG3B,WAAO,uBAAuB,OAAO,KAAK,CAAC,KAAK,WAAW,OAAO,MAAM,QAAQ,oBAAoB,KAAK,UAAU,OAAO;AAAA,EAC5H;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,IAC9B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAMH,SAAS,gBAAgBC,SAAQ;AAC/B,MAAI;AACF,WAAOA,QAAO;AAAA,EAChB,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAY,SAAS;AAG5B,SAAO,CAAC,EAAE,QAAQ,eAAe,QAAQ,gBAAgB,OAAO,QAAQ,mBAAmB,cAAc,QAAQ,eAAe,EAAE;AACpI;AAEA,SAAS,oBAAoB,SAAS;AACpC,MAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,SAAO,aAAa,WAAW,aAAa,YAAY,aAAa,YAAY,aAAa;AAChG;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,eAAe,OAAO,KAAK,QAAQ,QAAQ;AACpD;AAEA,SAAS,iBAAiB,SAAS;AACjC,SAAO,gBAAgB,OAAO,KAAK,QAAQ,aAAa,MAAM;AAChE;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,QAAQ,SAAS,YAAY,KAAK;AAC3C;AAEA,SAAS,gBAAgB,SAAS;AAChC,SAAO,QAAQ,SAAS,YAAY,KAAK;AAC3C;AAEA,SAAS,iBAAiB,SAAS;AACjC,MAAI,CAAC,QAAQ,aAAa,UAAU,KAAK,QAAQ,aAAa,QAAW;AACvE,WAAO;AAAA,EACT;AACA,MAAI,WAAW,QAAQ,aAAa,UAAU;AAC9C,SAAO,CAAC,EAAE,YAAY,CAAC,MAAM,SAAS,UAAU,EAAE,CAAC;AACrD;AAKA,SAAS,iBAAiB,SAAS;AACjC,MAAI,CAAC,iBAAiB,OAAO,GAAG;AAC9B,WAAO;AAAA,EACT;AAEA,QAAM,WAAW,SAAS,QAAQ,aAAa,UAAU,KAAK,IAAI,EAAE;AACpE,SAAO,MAAM,QAAQ,IAAI,KAAK;AAChC;AAEA,SAAS,yBAAyB,SAAS;AACzC,MAAI,WAAW,QAAQ,SAAS,YAAY;AAC5C,MAAI,YAAY,aAAa,WAAW,QAAQ;AAChD,SAAO,cAAc,UAAU,cAAc,cAAc,aAAa,YAAY,aAAa;AACnG;AAKA,SAAS,uBAAuB,SAAS;AAEvC,MAAI,cAAc,OAAO,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,SAAO,oBAAoB,OAAO,KAAK,iBAAiB,OAAO,KAAK,QAAQ,aAAa,iBAAiB,KAAK,iBAAiB,OAAO;AACzI;AAEA,SAAS,UAAU,MAAM;AAEvB,SAAO,KAAK,iBAAiB,KAAK,cAAc,eAAe;AACjE;AASA,IAAM,YAAN,MAAgB;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA;AAAA,EAEf,sBAAsB,MAAM,KAAK,yBAAyB;AAAA,EAC1D,oBAAoB,MAAM,KAAK,0BAA0B;AAAA;AAAA,EAEzD,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAChB,QAAI,KAAK,gBAAgB,KAAK,YAAY;AACxC,WAAK,sBAAsB,OAAO,KAAK,YAAY;AACnD,WAAK,sBAAsB,OAAO,KAAK,UAAU;AAAA,IACnD;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EACX,YAAY,UAAU,UAAU,SAAS,WAAW,eAAe,OACnE,WAAW;AACT,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,QAAI,CAAC,cAAc;AACjB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,UAAU;AACR,UAAM,cAAc,KAAK;AACzB,UAAM,YAAY,KAAK;AACvB,QAAI,aAAa;AACf,kBAAY,oBAAoB,SAAS,KAAK,mBAAmB;AACjE,kBAAY,OAAO;AAAA,IACrB;AACA,QAAI,WAAW;AACb,gBAAU,oBAAoB,SAAS,KAAK,iBAAiB;AAC7D,gBAAU,OAAO;AAAA,IACnB;AACA,SAAK,eAAe,KAAK,aAAa;AACtC,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AAEd,QAAI,KAAK,cAAc;AACrB,aAAO;AAAA,IACT;AACA,SAAK,QAAQ,kBAAkB,MAAM;AACnC,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,eAAe,KAAK,cAAc;AACvC,aAAK,aAAa,iBAAiB,SAAS,KAAK,mBAAmB;AAAA,MACtE;AACA,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa,KAAK,cAAc;AACrC,aAAK,WAAW,iBAAiB,SAAS,KAAK,iBAAiB;AAAA,MAClE;AAAA,IACF,CAAC;AACD,QAAI,KAAK,SAAS,YAAY;AAC5B,WAAK,SAAS,WAAW,aAAa,KAAK,cAAc,KAAK,QAAQ;AACtE,WAAK,SAAS,WAAW,aAAa,KAAK,YAAY,KAAK,SAAS,WAAW;AAChF,WAAK,eAAe;AAAA,IACtB;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,SAAS;AACpC,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,oBAAoB,OAAO,CAAC,CAAC;AAAA,IACxE,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mCAAmC,SAAS;AAC1C,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,0BAA0B,OAAO,CAAC,CAAC;AAAA,IAC9E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kCAAkC,SAAS;AACzC,WAAO,IAAI,QAAQ,aAAW;AAC5B,WAAK,iBAAiB,MAAM,QAAQ,KAAK,yBAAyB,OAAO,CAAC,CAAC;AAAA,IAC7E,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,OAAO;AAExB,UAAM,UAAU,KAAK,SAAS,iBAAiB,qBAAqB,KAAK,qBAA0B,KAAK,iBAAsB,KAAK,GAAG;AACtI,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAEvC,YAAI,QAAQ,CAAC,EAAE,aAAa,aAAa,KAAK,EAAE,GAAG;AACjD,kBAAQ,KAAK,gDAAgD,KAAK,yBAA8B,KAAK,iEAAsE,QAAQ,CAAC,CAAC;AAAA,QACvL,WAAW,QAAQ,CAAC,EAAE,aAAa,oBAAoB,KAAK,EAAE,GAAG;AAC/D,kBAAQ,KAAK,uDAAuD,KAAK,yBAA8B,KAAK,iEAAsE,QAAQ,CAAC,CAAC;AAAA,QAC9L;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS,SAAS;AACpB,aAAO,QAAQ,SAAS,QAAQ,CAAC,IAAI,KAAK,yBAAyB,KAAK,QAAQ;AAAA,IAClF;AACA,WAAO,QAAQ,SAAS,QAAQ,QAAQ,SAAS,CAAC,IAAI,KAAK,wBAAwB,KAAK,QAAQ;AAAA,EAClG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,SAAS;AAE3B,UAAM,oBAAoB,KAAK,SAAS,cAAc,wCAA6C;AACnG,QAAI,mBAAmB;AAErB,WAAK,OAAO,cAAc,eAAe,cAAc,kBAAkB,aAAa,mBAAmB,GAAG;AAC1G,gBAAQ,KAAK,2IAAqJ,iBAAiB;AAAA,MACrL;AAGA,WAAK,OAAO,cAAc,eAAe,cAAc,CAAC,KAAK,SAAS,YAAY,iBAAiB,GAAG;AACpG,gBAAQ,KAAK,0DAA0D,iBAAiB;AAAA,MAC1F;AACA,UAAI,CAAC,KAAK,SAAS,YAAY,iBAAiB,GAAG;AACjD,cAAM,iBAAiB,KAAK,yBAAyB,iBAAiB;AACtE,wBAAgB,MAAM,OAAO;AAC7B,eAAO,CAAC,CAAC;AAAA,MACX;AACA,wBAAkB,MAAM,OAAO;AAC/B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,0BAA0B,OAAO;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,0BAA0B,SAAS;AACjC,UAAM,oBAAoB,KAAK,mBAAmB,OAAO;AACzD,QAAI,mBAAmB;AACrB,wBAAkB,MAAM,OAAO;AAAA,IACjC;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB,SAAS;AAChC,UAAM,oBAAoB,KAAK,mBAAmB,KAAK;AACvD,QAAI,mBAAmB;AACrB,wBAAkB,MAAM,OAAO;AAAA,IACjC;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,yBAAyB,MAAM;AAC7B,QAAI,KAAK,SAAS,YAAY,IAAI,KAAK,KAAK,SAAS,WAAW,IAAI,GAAG;AACrE,aAAO;AAAA,IACT;AACA,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAM,gBAAgB,SAAS,CAAC,EAAE,aAAa,KAAK,UAAU,eAAe,KAAK,yBAAyB,SAAS,CAAC,CAAC,IAAI;AAC1H,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,wBAAwB,MAAM;AAC5B,QAAI,KAAK,SAAS,YAAY,IAAI,KAAK,KAAK,SAAS,WAAW,IAAI,GAAG;AACrE,aAAO;AAAA,IACT;AAEA,UAAM,WAAW,KAAK;AACtB,aAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,YAAM,gBAAgB,SAAS,CAAC,EAAE,aAAa,KAAK,UAAU,eAAe,KAAK,wBAAwB,SAAS,CAAC,CAAC,IAAI;AACzH,UAAI,eAAe;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,SAAS,KAAK,UAAU,cAAc,KAAK;AACjD,SAAK,sBAAsB,KAAK,UAAU,MAAM;AAChD,WAAO,UAAU,IAAI,qBAAqB;AAC1C,WAAO,UAAU,IAAI,uBAAuB;AAC5C,WAAO,aAAa,eAAe,MAAM;AACzC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,WAAW,QAAQ;AAGvC,gBAAY,OAAO,aAAa,YAAY,GAAG,IAAI,OAAO,gBAAgB,UAAU;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,SAAS;AACrB,QAAI,KAAK,gBAAgB,KAAK,YAAY;AACxC,WAAK,sBAAsB,SAAS,KAAK,YAAY;AACrD,WAAK,sBAAsB,SAAS,KAAK,UAAU;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,IAAI;AAEnB,QAAI,KAAK,WAAW;AAClB,sBAAgB,IAAI;AAAA,QAClB,UAAU,KAAK;AAAA,MACjB,CAAC;AAAA,IACH,OAAO;AACL,iBAAW,EAAE;AAAA,IACf;AAAA,EACF;AACF;AAIA,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,WAAW,OAAO,oBAAoB;AAAA,EACtC,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,SAAS,uBAAuB,OAAO;AAC5C,WAAO,IAAI,UAAU,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,WAAW,sBAAsB,KAAK,SAAS;AAAA,EACjH;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc,OAAO,UAAU;AAAA,EAC/B,oBAAoB,OAAO,gBAAgB;AAAA;AAAA,EAE3C;AAAA;AAAA,EAEA,4BAA4B;AAAA;AAAA,EAE5B,IAAI,UAAU;AACZ,WAAO,KAAK,WAAW,WAAW;AAAA,EACpC;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,UAAU;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,cAAc;AACZ,UAAM,WAAW,OAAO,QAAQ;AAChC,QAAI,SAAS,WAAW;AACtB,WAAK,YAAY,KAAK,kBAAkB,OAAO,KAAK,YAAY,eAAe,IAAI;AAAA,IACrF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,QAAQ;AAGxB,QAAI,KAAK,2BAA2B;AAClC,WAAK,0BAA0B,MAAM;AACrC,WAAK,4BAA4B;AAAA,IACnC;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,SAAK,WAAW,cAAc;AAC9B,QAAI,KAAK,aAAa;AACpB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,aAAa,CAAC,KAAK,UAAU,YAAY,GAAG;AACnD,WAAK,UAAU,cAAc;AAAA,IAC/B;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM,oBAAoB,QAAQ,aAAa;AAC/C,QAAI,qBAAqB,CAAC,kBAAkB,eAAe,KAAK,eAAe,KAAK,WAAW,YAAY,GAAG;AAC5G,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,4BAA4B,kCAAkC;AACnE,SAAK,WAAW,6BAA6B;AAAA,EAC/C;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IACpC,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,gBAAgB,WAAW,gBAAgB;AAAA,MACxD,aAAa,CAAC,GAAG,2BAA2B,eAAe,gBAAgB;AAAA,IAC7E;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,IACzB,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,+BAA+B,IAAI,eAAe,wBAAwB;AAAA,EAC9E,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,uCAAuC;AAC9C,SAAO;AACT;AAEA,IAAM,iCAAiC,IAAI,eAAe,gCAAgC;AAC1F,IAAI,YAAY;AAChB,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,UAAU,OAAO,MAAM;AAAA,EACvB,kBAAkB,OAAO,gCAAgC;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAM,eAAe,OAAO,8BAA8B;AAAA,MACxD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,eAAe,gBAAgB,KAAK,mBAAmB;AAAA,EAC9D;AAAA,EACA,SAAS,YAAY,MAAM;AACzB,UAAM,iBAAiB,KAAK;AAC5B,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,WAAW,KAAK,OAAO,KAAK,CAAC,MAAM,UAAU;AACpD,iBAAW,KAAK,CAAC;AAAA,IACnB,OAAO;AACL,OAAC,YAAY,QAAQ,IAAI;AAAA,IAC3B;AACA,SAAK,MAAM;AACX,iBAAa,KAAK,gBAAgB;AAClC,QAAI,CAAC,YAAY;AACf,mBAAa,kBAAkB,eAAe,aAAa,eAAe,aAAa;AAAA,IACzF;AACA,QAAI,YAAY,QAAQ,gBAAgB;AACtC,iBAAW,eAAe;AAAA,IAC5B;AAEA,SAAK,aAAa,aAAa,aAAa,UAAU;AACtD,QAAI,KAAK,aAAa,IAAI;AACxB,WAAK,yBAAyB,KAAK,aAAa,EAAE;AAAA,IACpD;AAMA,WAAO,KAAK,QAAQ,kBAAkB,MAAM;AAC1C,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,kBAAkB,IAAI,QAAQ,aAAW,KAAK,kBAAkB,OAAO;AAAA,MAC9E;AACA,mBAAa,KAAK,gBAAgB;AAClC,WAAK,mBAAmB,WAAW,MAAM;AACvC,aAAK,aAAa,cAAc;AAChC,YAAI,OAAO,aAAa,UAAU;AAChC,eAAK,mBAAmB,WAAW,MAAM,KAAK,MAAM,GAAG,QAAQ;AAAA,QACjE;AAGA,aAAK,kBAAkB;AACvB,aAAK,kBAAkB,KAAK,kBAAkB;AAAA,MAChD,GAAG,GAAG;AACN,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACN,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,cAAc;AAAA,IAClC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,iBAAa,KAAK,gBAAgB;AAClC,SAAK,cAAc,OAAO;AAC1B,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,kBAAkB,KAAK,kBAAkB;AAAA,EAChD;AAAA,EACA,qBAAqB;AACnB,UAAM,eAAe;AACrB,UAAM,mBAAmB,KAAK,UAAU,uBAAuB,YAAY;AAC3E,UAAM,SAAS,KAAK,UAAU,cAAc,KAAK;AAEjD,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,uBAAiB,CAAC,EAAE,OAAO;AAAA,IAC7B;AACA,WAAO,UAAU,IAAI,YAAY;AACjC,WAAO,UAAU,IAAI,qBAAqB;AAC1C,WAAO,aAAa,eAAe,MAAM;AACzC,WAAO,aAAa,aAAa,QAAQ;AACzC,WAAO,KAAK,sBAAsB,WAAW;AAC7C,SAAK,UAAU,KAAK,YAAY,MAAM;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,IAAI;AAO3B,UAAM,SAAS,KAAK,UAAU,iBAAiB,mDAAmD;AAClG,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAM,QAAQ,OAAO,CAAC;AACtB,YAAM,WAAW,MAAM,aAAa,WAAW;AAC/C,UAAI,CAAC,UAAU;AACb,cAAM,aAAa,aAAa,EAAE;AAAA,MACpC,WAAW,SAAS,QAAQ,EAAE,MAAM,IAAI;AACtC,cAAM,aAAa,aAAa,WAAW,MAAM,EAAE;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,IACvB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAKH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc,OAAO,UAAU;AAAA,EAC/B,iBAAiB,OAAO,aAAa;AAAA,EACrC,mBAAmB,OAAO,eAAe;AAAA,EACzC,UAAU,OAAO,MAAM;AAAA;AAAA,EAEvB,IAAI,aAAa;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc,UAAU,SAAS,UAAU,cAAc,QAAQ;AACtE,QAAI,KAAK,gBAAgB,OAAO;AAC9B,UAAI,KAAK,eAAe;AACtB,aAAK,cAAc,YAAY;AAC/B,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF,WAAW,CAAC,KAAK,eAAe;AAC9B,WAAK,gBAAgB,KAAK,QAAQ,kBAAkB,MAAM;AACxD,eAAO,KAAK,iBAAiB,QAAQ,KAAK,WAAW,EAAE,UAAU,MAAM;AAErE,gBAAM,cAAc,KAAK,YAAY,cAAc;AAGnD,cAAI,gBAAgB,KAAK,wBAAwB;AAC/C,iBAAK,eAAe,SAAS,aAAa,KAAK,aAAa,KAAK,QAAQ;AACzE,iBAAK,yBAAyB;AAAA,UAChC;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AAAA;AAAA,EAEd;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AAAA,EAC3D;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,YAAY;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IACnC,QAAQ;AAAA,MACN,YAAY,CAAC,GAAG,eAAe,YAAY;AAAA,MAC3C,UAAU,CAAC,GAAG,uBAAuB,UAAU;AAAA,IACjD;AAAA,IACA,UAAU,CAAC,aAAa;AAAA,EAC1B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAI;AAAA,CACH,SAAUC,mBAAkB;AAC3B,EAAAA,kBAAiBA,kBAAiB,MAAM,IAAI,CAAC,IAAI;AACjD,EAAAA,kBAAiBA,kBAAiB,gBAAgB,IAAI,CAAC,IAAI;AAC3D,EAAAA,kBAAiBA,kBAAiB,gBAAgB,IAAI,CAAC,IAAI;AAC7D,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAE9C,IAAM,2BAA2B;AAEjC,IAAM,2BAA2B;AAEjC,IAAM,sCAAsC;AAY5C,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,OAAO,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,cAAc;AACZ,SAAK,0BAA0B,OAAO,kBAAkB,EAAE,QAAQ,yBAAyB,EAAE,UAAU,MAAM;AAC3G,UAAI,KAAK,6BAA6B;AACpC,aAAK,8BAA8B;AACnC,aAAK,qCAAqC;AAAA,MAC5C;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB;AACpB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO,iBAAiB;AAAA,IAC1B;AAIA,UAAM,cAAc,KAAK,UAAU,cAAc,KAAK;AACtD,gBAAY,MAAM,kBAAkB;AACpC,gBAAY,MAAM,WAAW;AAC7B,SAAK,UAAU,KAAK,YAAY,WAAW;AAK3C,UAAM,iBAAiB,KAAK,UAAU,eAAe;AACrD,UAAM,gBAAgB,kBAAkB,eAAe,mBAAmB,eAAe,iBAAiB,WAAW,IAAI;AACzH,UAAM,iBAAiB,iBAAiB,cAAc,mBAAmB,IAAI,QAAQ,MAAM,EAAE;AAC7F,gBAAY,OAAO;AACnB,YAAQ,eAAe;AAAA;AAAA,MAErB,KAAK;AAAA;AAAA,MAEL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,iBAAiB;AAAA;AAAA,MAE1B,KAAK;AAAA;AAAA,MAEL,KAAK;AACH,eAAO,iBAAiB;AAAA,IAC5B;AACA,WAAO,iBAAiB;AAAA,EAC1B;AAAA,EACA,cAAc;AACZ,SAAK,wBAAwB,YAAY;AAAA,EAC3C;AAAA;AAAA,EAEA,uCAAuC;AACrC,QAAI,CAAC,KAAK,+BAA+B,KAAK,UAAU,aAAa,KAAK,UAAU,MAAM;AACxF,YAAM,cAAc,KAAK,UAAU,KAAK;AACxC,kBAAY,OAAO,qCAAqC,0BAA0B,wBAAwB;AAC1G,WAAK,8BAA8B;AACnC,YAAM,OAAO,KAAK,oBAAoB;AACtC,UAAI,SAAS,iBAAiB,gBAAgB;AAC5C,oBAAY,IAAI,qCAAqC,wBAAwB;AAAA,MAC/E,WAAW,SAAS,iBAAiB,gBAAgB;AACnD,oBAAY,IAAI,qCAAqC,wBAAwB;AAAA,MAC/E;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,0BAAyB;AAAA,IAClC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,aAAN,MAAM,YAAW;AAAA,EACf,cAAc;AACZ,WAAO,wBAAwB,EAAE,qCAAqC;AAAA,EACxE;AAAA,EACA,OAAO,OAAO,SAAS,mBAAmB,mBAAmB;AAC3D,WAAO,KAAK,qBAAqB,aAAY;AAAA,EAC/C;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,aAAa,cAAc,eAAe;AAAA,IACrE,SAAS,CAAC,aAAa,cAAc,eAAe;AAAA,EACtD,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,eAAe;AAAA,EAC3B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,YAAY,CAAC;AAAA,IACnF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,aAAa,cAAc,eAAe;AAAA,MACrE,SAAS,CAAC,aAAa,cAAc,eAAe;AAAA,IACtD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;ACz9BH,IAAM,WAAW,CAAC;AAElB,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,SAAS,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,MAAM,QAAQ;AAGZ,QAAI,KAAK,WAAW,MAAM;AACxB,gBAAU,KAAK;AAAA,IACjB;AACA,QAAI,CAAC,SAAS,eAAe,MAAM,GAAG;AACpC,eAAS,MAAM,IAAI;AAAA,IACrB;AACA,WAAO,GAAG,MAAM,GAAG,SAAS,MAAM,GAAG;AAAA,EACvC;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,cAAa;AAAA,IACtB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACvCH,SAAS,eAAe,UAAU,WAAW;AAC3C,MAAI,UAAU,QAAQ;AACpB,WAAO,UAAU,KAAK,cAAY,MAAM,QAAQ,CAAC;AAAA,EACnD;AACA,SAAO,MAAM,UAAU,MAAM,YAAY,MAAM,WAAW,MAAM;AAClE;;;ACRA,IAAAC,eAAsC;;;ACDtC,IAAAC,eAAwB;AACxB,IAAAC,oBAA+C;AAE/C,IAAM,yCAAyC;AAK/C,IAAM,YAAN,MAAgB;AAAA,EACd,mBAAmB,IAAI,qBAAQ;AAAA,EAC/B,SAAS,CAAC;AAAA,EACV,qBAAqB;AAAA;AAAA,EAErB,kBAAkB,CAAC;AAAA,EACnB;AAAA,EACA,gBAAgB,IAAI,qBAAQ;AAAA,EAC5B,eAAe,KAAK;AAAA,EACpB,YAAY,cAAc,QAAQ;AAChC,UAAM,oBAAoB,OAAO,QAAQ,qBAAqB,WAAW,OAAO,mBAAmB;AACnG,QAAI,QAAQ,eAAe;AACzB,WAAK,mBAAmB,OAAO;AAAA,IACjC;AACA,SAAK,OAAO,cAAc,eAAe,cAAc,aAAa,UAAU,aAAa,KAAK,UAAQ,OAAO,KAAK,aAAa,UAAU,GAAG;AAC5I,YAAM,IAAI,MAAM,0EAA0E;AAAA,IAC5F;AACA,SAAK,SAAS,YAAY;AAC1B,SAAK,iBAAiB,iBAAiB;AAAA,EACzC;AAAA,EACA,UAAU;AACR,SAAK,kBAAkB,CAAC;AACxB,SAAK,iBAAiB,SAAS;AAC/B,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA,EACA,4BAA4B,OAAO;AACjC,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,SAAS,OAAO;AACd,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM;AAGtB,QAAI,MAAM,OAAO,MAAM,IAAI,WAAW,GAAG;AACvC,WAAK,iBAAiB,KAAK,MAAM,IAAI,kBAAkB,CAAC;AAAA,IAC1D,WAAW,WAAW,KAAK,WAAW,KAAK,WAAW,QAAQ,WAAW,MAAM;AAC7E,WAAK,iBAAiB,KAAK,OAAO,aAAa,OAAO,CAAC;AAAA,IACzD;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,gBAAgB,SAAS;AAAA,EACvC;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,kBAAkB,CAAC;AAAA,EAC1B;AAAA,EACA,iBAAiB,mBAAmB;AAIlC,SAAK,iBAAiB,SAAK,uBAAI,YAAU,KAAK,gBAAgB,KAAK,MAAM,CAAC,OAAG,gCAAa,iBAAiB,OAAG,0BAAO,MAAM,KAAK,gBAAgB,SAAS,CAAC,OAAG,uBAAI,MAAM,KAAK,gBAAgB,KAAK,EAAE,EAAE,kBAAkB,CAAC,CAAC,EAAE,UAAU,iBAAe;AAGlP,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK;AAC/C,cAAM,SAAS,KAAK,qBAAqB,KAAK,KAAK,OAAO;AAC1D,cAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,YAAI,CAAC,KAAK,mBAAmB,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,EAAE,KAAK,EAAE,QAAQ,WAAW,MAAM,GAAG;AAC7G,eAAK,cAAc,KAAK,IAAI;AAC5B;AAAA,QACF;AAAA,MACF;AACA,WAAK,kBAAkB,CAAC;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;;;ADjEA,IAAM,iBAAN,MAAqB;AAAA,EACnB;AAAA,EACA,mBAAmB;AAAA,EACnB,cAAc,OAAO,IAAI;AAAA,EACzB,QAAQ;AAAA,EACR,yBAAyB,0BAAa;AAAA,EACtC;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA,uBAAuB,CAAC;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,IACf,SAAS;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,UAAQ,KAAK;AAAA,EAChC,YAAY,QAAQ,UAAU;AAC5B,SAAK,SAAS;AAId,QAAI,kBAAkB,WAAW;AAC/B,WAAK,2BAA2B,OAAO,QAAQ,UAAU,cAAY,KAAK,cAAc,SAAS,QAAQ,CAAC,CAAC;AAAA,IAC7G,WAAW,SAAS,MAAM,GAAG;AAC3B,UAAI,CAAC,aAAa,OAAO,cAAc,eAAe,YAAY;AAChE,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AACA,WAAK,aAAa,OAAO,MAAM,KAAK,cAAc,OAAO,CAAC,GAAG;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,IAAI,qBAAQ;AAAA;AAAA,EAErB,SAAS,IAAI,qBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,cAAc,WAAW;AACvB,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,aAAa,MAAM;AAC1B,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,UAAU,MAAM;AACtC,SAAK,YAAY;AACjB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,0BAA0B,WAAW;AACnC,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,MAAM;AAC5B,SAAK,uBAAuB;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,mBAAmB,KAAK;AACpC,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,YAAMC,SAAQ,KAAK,eAAe;AAClC,UAAIA,OAAM,SAAS,KAAKA,OAAM,KAAK,UAAQ,OAAO,KAAK,aAAa,UAAU,GAAG;AAC/E,cAAM,MAAM,8EAA8E;AAAA,MAC5F;AAAA,IACF;AACA,SAAK,uBAAuB,YAAY;AACxC,UAAM,QAAQ,KAAK,eAAe;AAClC,SAAK,aAAa,IAAI,UAAU,OAAO;AAAA,MACrC,kBAAkB,OAAO,qBAAqB,WAAW,mBAAmB;AAAA,MAC5E,eAAe,UAAQ,KAAK,iBAAiB,IAAI;AAAA,IACnD,CAAC;AACD,SAAK,yBAAyB,KAAK,WAAW,aAAa,UAAU,UAAQ;AAC3E,WAAK,cAAc,IAAI;AAAA,IACzB,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,YAAY,MAAM;AACvB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,UAAU,MAAM;AAC7B,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,UAAU,MAAM,QAAQ,IAAI;AACzC,SAAK,iBAAiB;AAAA,MACpB;AAAA,MACA;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,MAAM;AAClB,UAAM,qBAAqB,KAAK,YAAY;AAC5C,SAAK,iBAAiB,IAAI;AAC1B,QAAI,KAAK,YAAY,MAAM,oBAAoB;AAC7C,WAAK,OAAO,KAAK,KAAK,gBAAgB;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,UAAM,UAAU,MAAM;AACtB,UAAM,YAAY,CAAC,UAAU,WAAW,WAAW,UAAU;AAC7D,UAAM,oBAAoB,UAAU,MAAM,cAAY;AACpD,aAAO,CAAC,MAAM,QAAQ,KAAK,KAAK,qBAAqB,QAAQ,QAAQ,IAAI;AAAA,IAC3E,CAAC;AACD,YAAQ,SAAS;AAAA,MACf,KAAK;AACH,aAAK,OAAO,KAAK;AACjB;AAAA,MACF,KAAK;AACH,YAAI,KAAK,aAAa,mBAAmB;AACvC,eAAK,kBAAkB;AACvB;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,aAAa,mBAAmB;AACvC,eAAK,sBAAsB;AAC3B;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,gBAAgB,QAAQ,KAAK,sBAAsB,IAAI,KAAK,kBAAkB;AACnF;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,gBAAgB,QAAQ,KAAK,kBAAkB,IAAI,KAAK,sBAAsB;AACnF;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,mBAAmB;AACxB;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,mBAAmB;AACzC,eAAK,kBAAkB;AACvB;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,WAAW,mBAAmB;AACpD,gBAAM,cAAc,KAAK,mBAAmB,KAAK,eAAe;AAChE,eAAK,sBAAsB,cAAc,IAAI,cAAc,GAAG,CAAC;AAC/D;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF,KAAK;AACH,YAAI,KAAK,eAAe,WAAW,mBAAmB;AACpD,gBAAM,cAAc,KAAK,mBAAmB,KAAK,eAAe;AAChE,gBAAM,cAAc,KAAK,eAAe,EAAE;AAC1C,eAAK,sBAAsB,cAAc,cAAc,cAAc,cAAc,GAAG,EAAE;AACxF;AAAA,QACF,OAAO;AACL;AAAA,QACF;AAAA,MACF;AACE,YAAI,qBAAqB,eAAe,OAAO,UAAU,GAAG;AAC1D,eAAK,YAAY,UAAU,KAAK;AAAA,QAClC;AAGA;AAAA,IACJ;AACA,SAAK,YAAY,MAAM;AACvB,UAAM,eAAe;AAAA,EACvB;AAAA;AAAA,EAEA,IAAI,kBAAkB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA,EAEA,WAAW;AACT,WAAO,CAAC,CAAC,KAAK,cAAc,KAAK,WAAW,SAAS;AAAA,EACvD;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,sBAAsB,GAAG,CAAC;AAAA,EACjC;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,sBAAsB,KAAK,eAAe,EAAE,SAAS,GAAG,EAAE;AAAA,EACjE;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,mBAAmB,IAAI,KAAK,mBAAmB,IAAI,KAAK,sBAAsB,CAAC;AAAA,EACtF;AAAA;AAAA,EAEA,wBAAwB;AACtB,SAAK,mBAAmB,KAAK,KAAK,QAAQ,KAAK,kBAAkB,IAAI,KAAK,sBAAsB,EAAE;AAAA,EACpG;AAAA,EACA,iBAAiB,MAAM;AACrB,UAAM,YAAY,KAAK,eAAe;AACtC,UAAM,QAAQ,OAAO,SAAS,WAAW,OAAO,UAAU,QAAQ,IAAI;AACtE,UAAM,aAAa,UAAU,KAAK;AAElC,SAAK,YAAY,IAAI,cAAc,OAAO,OAAO,UAAU;AAC3D,SAAK,mBAAmB;AACxB,SAAK,YAAY,4BAA4B,KAAK;AAAA,EACpD;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,uBAAuB,YAAY;AACxC,SAAK,0BAA0B,YAAY;AAC3C,SAAK,YAAY,QAAQ;AACzB,SAAK,YAAY,QAAQ;AACzB,SAAK,OAAO,SAAS;AACrB,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,OAAO;AAC3B,SAAK,QAAQ,KAAK,qBAAqB,KAAK,IAAI,KAAK,wBAAwB,KAAK;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,OAAO;AAC1B,UAAM,QAAQ,KAAK,eAAe;AAClC,aAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,KAAK;AACtC,YAAM,SAAS,KAAK,mBAAmB,QAAQ,IAAI,MAAM,UAAU,MAAM;AACzE,YAAM,OAAO,MAAM,KAAK;AACxB,UAAI,CAAC,KAAK,iBAAiB,IAAI,GAAG;AAChC,aAAK,cAAc,KAAK;AACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,OAAO;AAC7B,SAAK,sBAAsB,KAAK,mBAAmB,OAAO,KAAK;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,OAAO,eAAe;AAC1C,UAAM,QAAQ,KAAK,eAAe;AAClC,QAAI,CAAC,MAAM,KAAK,GAAG;AACjB;AAAA,IACF;AACA,WAAO,KAAK,iBAAiB,MAAM,KAAK,CAAC,GAAG;AAC1C,eAAS;AACT,UAAI,CAAC,MAAM,KAAK,GAAG;AACjB;AAAA,MACF;AAAA,IACF;AACA,SAAK,cAAc,KAAK;AAAA,EAC1B;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,SAAS,KAAK,MAAM,GAAG;AACzB,aAAO,KAAK,OAAO;AAAA,IACrB;AACA,WAAO,KAAK,kBAAkB,YAAY,KAAK,OAAO,QAAQ,IAAI,KAAK;AAAA,EACzE;AAAA;AAAA,EAEA,cAAc,UAAU;AACtB,SAAK,YAAY,SAAS,QAAQ;AAClC,UAAM,aAAa,KAAK,YAAY;AACpC,QAAI,YAAY;AACd,YAAM,WAAW,SAAS,QAAQ,UAAU;AAC5C,UAAI,WAAW,MAAM,aAAa,KAAK,kBAAkB;AACvD,aAAK,mBAAmB;AACxB,aAAK,YAAY,4BAA4B,QAAQ;AAAA,MACvD;AAAA,IACF;AAAA,EACF;AACF;;;AEhWA,IAAM,6BAAN,cAAyC,eAAe;AAAA,EACtD,cAAc,OAAO;AACnB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,kBAAkB;AAAA,IACpC;AACA,UAAM,cAAc,KAAK;AACzB,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,gBAAgB;AAAA,IAClC;AAAA,EACF;AACF;;;ACEA,IAAAC,eAAwB;;;ACZxB,IAAAC,eAAwD;AACxD,IAAAC,oBAAqB;;;ACFrB,IAAAC,eAAiC;AAMjC,SAAS,iBAAiB,MAAM;AAC9B,MAAI,KAAC,2BAAa,IAAI,GAAG;AACvB,eAAO,iBAAG,IAAI;AAAA,EAChB;AACA,SAAO;AACT;;;ADAA,IAAM,iBAAN,MAAqB;AAAA;AAAA,EAEnB,mBAAmB;AAAA;AAAA,EAEnB,cAAc;AAAA;AAAA,EAEd,+BAA+B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASzB,mBAAmB,WAAS;AAAA;AAAA,EAE5B,aAAa,UAAQ;AAAA;AAAA,EAErB,SAAS,CAAC;AAAA,EACV;AAAA,EACA,yBAAyB,0BAAa;AAAA,EACtC,qBAAqB;AAAA,EACrB,mBAAmB;AACjB,QAAI,KAAK,sBAAsB,KAAK,OAAO,WAAW,GAAG;AACvD;AAAA,IACF;AACA,QAAI,cAAc;AAClB,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,gBAAgB,KAAK,OAAO,CAAC,CAAC,GAAG;AACnF,sBAAc;AACd;AAAA,MACF;AAAA,IACF;AACA,UAAM,aAAa,KAAK,OAAO,WAAW;AAG1C,QAAI,WAAW,eAAe;AAC5B,WAAK,aAAa,QAAQ;AAC1B,WAAK,mBAAmB;AACxB,WAAK,cAAc;AACnB,WAAK,YAAY,4BAA4B,WAAW;AACxD,iBAAW,cAAc;AAAA,IAC3B,OAAO;AAEL,WAAK,UAAU,WAAW;AAAA,IAC5B;AACA,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,OAAO,QAAQ;AAIzB,QAAI,iBAAiB,WAAW;AAC9B,WAAK,SAAS,MAAM,QAAQ;AAC5B,YAAM,QAAQ,UAAU,cAAY;AAClC,aAAK,SAAS,SAAS,QAAQ;AAC/B,aAAK,YAAY,SAAS,KAAK,MAAM;AACrC,aAAK,uBAAuB,KAAK,MAAM;AACvC,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH,eAAW,2BAAa,KAAK,GAAG;AAC9B,YAAM,UAAU,cAAY;AAC1B,aAAK,SAAS;AACd,aAAK,YAAY,SAAS,QAAQ;AAClC,aAAK,uBAAuB,QAAQ;AACpC,aAAK,iBAAiB;AAAA,MACxB,CAAC;AAAA,IACH,OAAO;AACL,WAAK,SAAS;AACd,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,OAAO,OAAO,gCAAgC,WAAW;AAC3D,WAAK,+BAA+B,OAAO;AAAA,IAC7C;AACA,QAAI,OAAO,uBAAuB;AAChC,WAAK,yBAAyB,OAAO;AAAA,IACvC;AACA,QAAI,OAAO,eAAe;AACxB,WAAK,mBAAmB,OAAO;AAAA,IACjC;AACA,QAAI,OAAO,SAAS;AAClB,WAAK,aAAa,OAAO;AAAA,IAC3B;AACA,QAAI,OAAO,OAAO,8BAA8B,aAAa;AAC3D,WAAK,cAAc,OAAO,yBAAyB;AAAA,IACrD;AAAA,EACF;AAAA;AAAA,EAEA,SAAS,IAAI,qBAAQ;AAAA;AAAA,EAErB,UAAU;AACR,SAAK,uBAAuB,YAAY;AACxC,SAAK,YAAY,QAAQ;AACzB,SAAK,OAAO,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO;AACf,UAAM,MAAM,MAAM;AAClB,YAAQ,KAAK;AAAA,MACX,KAAK;AAEH;AAAA,MACF,KAAK;AACH,aAAK,eAAe;AACpB;AAAA,MACF,KAAK;AACH,aAAK,mBAAmB;AACxB;AAAA,MACF,KAAK;AACH,aAAK,2BAA2B,QAAQ,KAAK,qBAAqB,IAAI,KAAK,mBAAmB;AAC9F;AAAA,MACF,KAAK;AACH,aAAK,2BAA2B,QAAQ,KAAK,mBAAmB,IAAI,KAAK,qBAAqB;AAC9F;AAAA,MACF,KAAK;AACH,aAAK,gBAAgB;AACrB;AAAA,MACF,KAAK;AACH,aAAK,eAAe;AACpB;AAAA,MACF,KAAK;AAAA,MACL,KAAK;AACH,aAAK,qBAAqB;AAC1B;AAAA,MACF;AACE,YAAI,MAAM,QAAQ,KAAK;AACrB,eAAK,kCAAkC;AACvC;AAAA,QACF;AACA,aAAK,YAAY,UAAU,KAAK;AAGhC;AAAA,IACJ;AAEA,SAAK,YAAY,MAAM;AACvB,UAAM,eAAe;AAAA,EACvB;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,UAAU,KAAK,4BAA4B,EAAE,CAAC;AAAA,EACrD;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,UAAU,KAAK,gCAAgC,KAAK,OAAO,MAAM,CAAC;AAAA,EACzE;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,UAAU,KAAK,4BAA4B,KAAK,gBAAgB,CAAC;AAAA,EACxE;AAAA;AAAA,EAEA,qBAAqB;AACnB,SAAK,UAAU,KAAK,gCAAgC,KAAK,gBAAgB,CAAC;AAAA,EAC5E;AAAA,EACA,UAAU,aAAa,UAAU,CAAC,GAAG;AAEnC,YAAQ,oBAAoB;AAC5B,QAAI,QAAQ,OAAO,gBAAgB,WAAW,cAAc,KAAK,OAAO,UAAU,UAAQ,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,WAAW,CAAC;AAChJ,QAAI,QAAQ,KAAK,SAAS,KAAK,OAAO,QAAQ;AAC5C;AAAA,IACF;AACA,UAAM,aAAa,KAAK,OAAO,KAAK;AAEpC,QAAI,KAAK,gBAAgB,QAAQ,KAAK,WAAW,UAAU,MAAM,KAAK,WAAW,KAAK,WAAW,GAAG;AAClG;AAAA,IACF;AACA,UAAM,qBAAqB,KAAK;AAChC,SAAK,cAAc,cAAc;AACjC,SAAK,mBAAmB;AACxB,SAAK,YAAY,4BAA4B,KAAK;AAClD,SAAK,aAAa,MAAM;AACxB,wBAAoB,QAAQ;AAC5B,QAAI,QAAQ,iBAAiB;AAC3B,WAAK,OAAO,KAAK,KAAK,WAAW;AAAA,IACnC;AACA,QAAI,KAAK,8BAA8B;AACrC,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,uBAAuB,UAAU;AAC/B,UAAM,aAAa,KAAK;AACxB,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,UAAM,WAAW,SAAS,UAAU,UAAQ,KAAK,WAAW,IAAI,MAAM,KAAK,WAAW,UAAU,CAAC;AACjG,QAAI,WAAW,MAAM,aAAa,KAAK,kBAAkB;AACvD,WAAK,mBAAmB;AACxB,WAAK,YAAY,4BAA4B,QAAQ;AAAA,IACvD;AAAA,EACF;AAAA,EACA,cAAc,kBAAkB;AAC9B,SAAK,aAAa,IAAI,UAAU,KAAK,QAAQ;AAAA,MAC3C,kBAAkB,OAAO,qBAAqB,WAAW,mBAAmB;AAAA,MAC5E,eAAe,UAAQ,KAAK,iBAAiB,IAAI;AAAA,IACnD,CAAC;AACD,SAAK,yBAAyB,KAAK,WAAW,aAAa,UAAU,UAAQ;AAC3E,WAAK,UAAU,IAAI;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,4BAA4B,eAAe;AACzC,aAAS,IAAI,gBAAgB,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3D,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC,GAAG;AAC1C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,gCAAgC,eAAe;AAC7C,aAAS,IAAI,gBAAgB,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAI,CAAC,KAAK,iBAAiB,KAAK,OAAO,CAAC,CAAC,GAAG;AAC1C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB;AACrB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,KAAK,uBAAuB,GAAG;AACjC,WAAK,YAAY,SAAS;AAAA,IAC5B,OAAO;AACL,YAAM,SAAS,KAAK,YAAY,UAAU;AAC1C,UAAI,CAAC,UAAU,KAAK,iBAAiB,MAAM,GAAG;AAC5C;AAAA,MACF;AACA,WAAK,UAAU,MAAM;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,uBAAuB,GAAG;AAClC,WAAK,YAAY,OAAO;AAAA,IAC1B,OAAO;AACL,uBAAiB,KAAK,YAAY,YAAY,CAAC,EAAE,SAAK,wBAAK,CAAC,CAAC,EAAE,UAAU,cAAY;AACnF,cAAM,aAAa,SAAS,KAAK,WAAS,CAAC,KAAK,iBAAiB,KAAK,CAAC;AACvE,YAAI,CAAC,YAAY;AACf;AAAA,QACF;AACA,aAAK,UAAU,UAAU;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,QAAI,CAAC,KAAK,aAAa;AACrB,aAAO;AAAA,IACT;AACA,WAAO,OAAO,KAAK,YAAY,eAAe,YAAY,KAAK,YAAY,aAAa,KAAK,YAAY,WAAW;AAAA,EACtH;AAAA,EACA,gBAAgB,MAAM;AACpB,WAAO,OAAO,KAAK,eAAe,YAAY,KAAK,aAAa,KAAK,aAAa;AAAA,EACpF;AAAA;AAAA,EAEA,oCAAoC;AAClC,QAAI,CAAC,KAAK,aAAa;AACrB;AAAA,IACF;AACA,UAAM,SAAS,KAAK,YAAY,UAAU;AAC1C,QAAI;AACJ,QAAI,CAAC,QAAQ;AACX,0BAAgB,iBAAG,KAAK,OAAO,OAAO,UAAQ,KAAK,UAAU,MAAM,IAAI,CAAC;AAAA,IAC1E,OAAO;AACL,sBAAgB,iBAAiB,OAAO,YAAY,CAAC;AAAA,IACvD;AACA,kBAAc,SAAK,wBAAK,CAAC,CAAC,EAAE,UAAU,WAAS;AAC7C,iBAAW,QAAQ,OAAO;AACxB,aAAK,OAAO;AAAA,MACd;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,uBAAuB;AACrB,SAAK,aAAa,SAAS;AAAA,EAC7B;AACF;AAMA,SAAS,2BAA2B;AAClC,SAAO,CAAC,OAAO,YAAY,IAAI,eAAe,OAAO,OAAO;AAC9D;AAEA,IAAM,mBAAmB,IAAI,eAAe,oBAAoB;AAAA,EAC9D,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;;;ADzTD,IAAAC,oBAAO;AAcP,IAAM,eAAe;AAKrB,SAAS,oBAAoB,IAAI,MAAM,IAAI;AACzC,QAAM,MAAM,oBAAoB,IAAI,IAAI;AACxC,OAAK,GAAG,KAAK;AACb,MAAI,IAAI,KAAK,gBAAc,WAAW,KAAK,MAAM,EAAE,GAAG;AACpD;AAAA,EACF;AACA,MAAI,KAAK,EAAE;AACX,KAAG,aAAa,MAAM,IAAI,KAAK,YAAY,CAAC;AAC9C;AAKA,SAAS,uBAAuB,IAAI,MAAM,IAAI;AAC5C,QAAM,MAAM,oBAAoB,IAAI,IAAI;AACxC,OAAK,GAAG,KAAK;AACb,QAAM,cAAc,IAAI,OAAO,SAAO,QAAQ,EAAE;AAChD,MAAI,YAAY,QAAQ;AACtB,OAAG,aAAa,MAAM,YAAY,KAAK,YAAY,CAAC;AAAA,EACtD,OAAO;AACL,OAAG,gBAAgB,IAAI;AAAA,EACzB;AACF;AAKA,SAAS,oBAAoB,IAAI,MAAM;AAErC,QAAM,YAAY,GAAG,aAAa,IAAI;AACtC,SAAO,WAAW,MAAM,MAAM,KAAK,CAAC;AACtC;AAaA,IAAM,4BAA4B;AAMlC,IAAM,iCAAiC;AAEvC,IAAI,SAAS;AAMb,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,QAAQ;AAAA;AAAA,EAE3B,mBAAmB,oBAAI,IAAI;AAAA;AAAA,EAE3B,qBAAqB;AAAA;AAAA,EAErB,MAAM,GAAG,QAAQ;AAAA,EACjB,cAAc;AACZ,WAAO,sBAAsB,EAAE,KAAK,qBAAqB;AACzD,SAAK,MAAM,OAAO,MAAM,IAAI,MAAM;AAAA,EACpC;AAAA,EACA,SAAS,aAAa,SAAS,MAAM;AACnC,QAAI,CAAC,KAAK,gBAAgB,aAAa,OAAO,GAAG;AAC/C;AAAA,IACF;AACA,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,QAAI,OAAO,YAAY,UAAU;AAE/B,mBAAa,SAAS,KAAK,GAAG;AAC9B,WAAK,iBAAiB,IAAI,KAAK;AAAA,QAC7B,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH,WAAW,CAAC,KAAK,iBAAiB,IAAI,GAAG,GAAG;AAC1C,WAAK,sBAAsB,SAAS,IAAI;AAAA,IAC1C;AACA,QAAI,CAAC,KAAK,6BAA6B,aAAa,GAAG,GAAG;AACxD,WAAK,qBAAqB,aAAa,GAAG;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,kBAAkB,aAAa,SAAS,MAAM;AAC5C,QAAI,CAAC,WAAW,CAAC,KAAK,eAAe,WAAW,GAAG;AACjD;AAAA,IACF;AACA,UAAM,MAAM,OAAO,SAAS,IAAI;AAChC,QAAI,KAAK,6BAA6B,aAAa,GAAG,GAAG;AACvD,WAAK,wBAAwB,aAAa,GAAG;AAAA,IAC/C;AAGA,QAAI,OAAO,YAAY,UAAU;AAC/B,YAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,UAAI,qBAAqB,kBAAkB,mBAAmB,GAAG;AAC/D,aAAK,sBAAsB,GAAG;AAAA,MAChC;AAAA,IACF;AACA,QAAI,KAAK,oBAAoB,WAAW,WAAW,GAAG;AACpD,WAAK,mBAAmB,OAAO;AAC/B,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,UAAM,oBAAoB,KAAK,UAAU,iBAAiB,IAAI,8BAA8B,KAAK,KAAK,GAAG,IAAI;AAC7G,aAAS,IAAI,GAAG,IAAI,kBAAkB,QAAQ,KAAK;AACjD,WAAK,kCAAkC,kBAAkB,CAAC,CAAC;AAC3D,wBAAkB,CAAC,EAAE,gBAAgB,8BAA8B;AAAA,IACrE;AACA,SAAK,oBAAoB,OAAO;AAChC,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,SAAS,MAAM;AACnC,UAAM,iBAAiB,KAAK,UAAU,cAAc,KAAK;AACzD,iBAAa,gBAAgB,KAAK,GAAG;AACrC,mBAAe,cAAc;AAC7B,QAAI,MAAM;AACR,qBAAe,aAAa,QAAQ,IAAI;AAAA,IAC1C;AACA,SAAK,yBAAyB;AAC9B,SAAK,mBAAmB,YAAY,cAAc;AAClD,SAAK,iBAAiB,IAAI,OAAO,SAAS,IAAI,GAAG;AAAA,MAC/C;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,sBAAsB,KAAK;AACzB,SAAK,iBAAiB,IAAI,GAAG,GAAG,gBAAgB,OAAO;AACvD,SAAK,iBAAiB,OAAO,GAAG;AAAA,EAClC;AAAA;AAAA,EAEA,2BAA2B;AACzB,QAAI,KAAK,oBAAoB;AAC3B;AAAA,IACF;AACA,UAAM,qBAAqB;AAC3B,UAAM,mBAAmB,KAAK,UAAU,iBAAiB,IAAI,kBAAkB,qBAAqB;AACpG,aAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAKhD,uBAAiB,CAAC,EAAE,OAAO;AAAA,IAC7B;AACA,UAAM,oBAAoB,KAAK,UAAU,cAAc,KAAK;AAK5D,sBAAkB,MAAM,aAAa;AAGrC,sBAAkB,UAAU,IAAI,kBAAkB;AAClD,sBAAkB,UAAU,IAAI,qBAAqB;AACrD,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,wBAAkB,aAAa,YAAY,QAAQ;AAAA,IACrD;AACA,SAAK,UAAU,KAAK,YAAY,iBAAiB;AACjD,SAAK,qBAAqB;AAAA,EAC5B;AAAA;AAAA,EAEA,kCAAkC,SAAS;AAEzC,UAAM,uBAAuB,oBAAoB,SAAS,kBAAkB,EAAE,OAAO,QAAM,GAAG,QAAQ,yBAAyB,KAAK,CAAC;AACrI,YAAQ,aAAa,oBAAoB,qBAAqB,KAAK,GAAG,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,SAAS,KAAK;AACjC,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AAGvD,wBAAoB,SAAS,oBAAoB,kBAAkB,eAAe,EAAE;AACpF,YAAQ,aAAa,gCAAgC,KAAK,GAAG;AAC7D,sBAAkB;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAwB,SAAS,KAAK;AACpC,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,sBAAkB;AAClB,2BAAuB,SAAS,oBAAoB,kBAAkB,eAAe,EAAE;AACvF,YAAQ,gBAAgB,8BAA8B;AAAA,EACxD;AAAA;AAAA,EAEA,6BAA6B,SAAS,KAAK;AACzC,UAAM,eAAe,oBAAoB,SAAS,kBAAkB;AACpE,UAAM,oBAAoB,KAAK,iBAAiB,IAAI,GAAG;AACvD,UAAM,YAAY,qBAAqB,kBAAkB,eAAe;AACxE,WAAO,CAAC,CAAC,aAAa,aAAa,QAAQ,SAAS,KAAK;AAAA,EAC3D;AAAA;AAAA,EAEA,gBAAgB,SAAS,SAAS;AAChC,QAAI,CAAC,KAAK,eAAe,OAAO,GAAG;AACjC,aAAO;AAAA,IACT;AACA,QAAI,WAAW,OAAO,YAAY,UAAU;AAI1C,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,WAAW,OAAO,KAAK,GAAG,OAAO,GAAG,KAAK;AAChE,UAAM,YAAY,QAAQ,aAAa,YAAY;AAGnD,WAAO,iBAAiB,CAAC,aAAa,UAAU,KAAK,MAAM,iBAAiB;AAAA,EAC9E;AAAA;AAAA,EAEA,eAAe,SAAS;AACtB,WAAO,QAAQ,aAAa,KAAK,UAAU;AAAA,EAC7C;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,eAAc;AAAA,IACvB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,SAAS,OAAO,SAAS,MAAM;AAC7B,SAAO,OAAO,YAAY,WAAW,GAAG,QAAQ,EAAE,IAAI,OAAO,KAAK;AACpE;AAEA,SAAS,aAAa,SAAS,WAAW;AACxC,MAAI,CAAC,QAAQ,IAAI;AACf,YAAQ,KAAK,GAAG,yBAAyB,IAAI,SAAS,IAAI,QAAQ;AAAA,EACpE;AACF;AA2FA,IAAM,wBAAN,cAAoC,UAAU;AAAA,EAC5C;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW;AAChB,QAAI,KAAK,UAAU;AACjB,WAAK,kBAAkB,SAAS,IAAI;AAAA,IACtC,OAAO;AACL,WAAK,kBAAkB,WAAW,IAAI;AAAA,IACxC;AAAA,EACF;AAAA,EACA,YAAY,UAAU,UAAU,SAAS,WAAW,mBAAmB,gBAAgB,QAAQ,UAAU;AACvG,UAAM,UAAU,UAAU,SAAS,WAAW,OAAO,OAAO,QAAQ;AACpE,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,SAAS,IAAI;AAAA,EACtC;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,kBAAkB,WAAW,IAAI;AACtC,UAAM,QAAQ;AAAA,EAChB;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,eAAe,aAAa,IAAI;AACrC,SAAK,cAAc,IAAI;AAAA,EACzB;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,eAAe,WAAW,IAAI;AACnC,SAAK,cAAc,KAAK;AAAA,EAC1B;AACF;AAMA,IAAM,sCAAN,MAA0C;AAAA;AAAA,EAExC,YAAY;AAAA;AAAA,EAEZ,aAAa,WAAW;AAEtB,QAAI,KAAK,WAAW;AAClB,gBAAU,UAAU,oBAAoB,SAAS,KAAK,WAAW,IAAI;AAAA,IACvE;AACA,SAAK,YAAY,OAAK,KAAK,WAAW,WAAW,CAAC;AAClD,cAAU,QAAQ,kBAAkB,MAAM;AACxC,gBAAU,UAAU,iBAAiB,SAAS,KAAK,WAAW,IAAI;AAAA,IACpE,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,WAAW,WAAW;AACpB,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,cAAU,UAAU,oBAAoB,SAAS,KAAK,WAAW,IAAI;AACrE,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,WAAW,OAAO;AAC3B,UAAM,SAAS,MAAM;AACrB,UAAM,gBAAgB,UAAU;AAGhC,QAAI,UAAU,CAAC,cAAc,SAAS,MAAM,KAAK,CAAC,OAAO,UAAU,sBAAsB,GAAG;AAI1F,iBAAW,MAAM;AAEf,YAAI,UAAU,WAAW,CAAC,cAAc,SAAS,UAAU,UAAU,aAAa,GAAG;AACnF,oBAAU,0BAA0B;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAGA,IAAM,4BAA4B,IAAI,eAAe,2BAA2B;AAGhF,IAAM,mBAAN,MAAM,kBAAiB;AAAA;AAAA;AAAA,EAGrB,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,SAAS,WAAW;AAElB,SAAK,kBAAkB,KAAK,gBAAgB,OAAO,QAAM,OAAO,SAAS;AACzE,QAAI,QAAQ,KAAK;AACjB,QAAI,MAAM,QAAQ;AAChB,YAAM,MAAM,SAAS,CAAC,EAAE,SAAS;AAAA,IACnC;AACA,UAAM,KAAK,SAAS;AACpB,cAAU,QAAQ;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,WAAW;AACpB,cAAU,SAAS;AACnB,UAAM,QAAQ,KAAK;AACnB,UAAM,IAAI,MAAM,QAAQ,SAAS;AACjC,QAAI,MAAM,IAAI;AACZ,YAAM,OAAO,GAAG,CAAC;AACjB,UAAI,MAAM,QAAQ;AAChB,cAAM,MAAM,SAAS,CAAC,EAAE,QAAQ;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,yBAAyB,mBAAmB;AACjE,WAAO,KAAK,qBAAqB,mBAAkB;AAAA,EACrD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,kBAAiB;AAAA,IAC1B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,WAAW,OAAO,oBAAoB;AAAA,EACtC,UAAU,OAAO,MAAM;AAAA,EACvB,oBAAoB,OAAO,gBAAgB;AAAA,EAC3C,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AACZ,UAAM,gBAAgB,OAAO,2BAA2B;AAAA,MACtD,UAAU;AAAA,IACZ,CAAC;AAED,SAAK,iBAAiB,iBAAiB,IAAI,oCAAoC;AAAA,EACjF;AAAA,EACA,OAAO,SAAS,SAAS;AAAA,IACvB,OAAO;AAAA,EACT,GAAG;AACD,QAAI;AACJ,QAAI,OAAO,WAAW,WAAW;AAC/B,qBAAe;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,qBAAe;AAAA,IACjB;AACA,WAAO,IAAI,sBAAsB,SAAS,KAAK,UAAU,KAAK,SAAS,KAAK,WAAW,KAAK,mBAAmB,KAAK,gBAAgB,cAAc,KAAK,SAAS;AAAA,EAClK;AAAA,EACA,OAAO,OAAO,SAAS,qCAAqC,mBAAmB;AAC7E,WAAO,KAAK,qBAAqB,+BAA8B;AAAA,EACjE;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,8BAA6B;AAAA,IACtC,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;;;AGnjBH,IAAM,yBAAyB,IAAI,eAAe,qBAAqB;AAAA,EACrE,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AASD,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AAGZ,WAAO,wBAAwB,EAAE,qCAAqC;AAAA,EACxE;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,UAAU;AAAA,IACpB,SAAS,CAAC,UAAU;AAAA,EACtB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,YAAY,UAAU;AAAA,EAClC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,UAAU;AAAA,MACpB,SAAS,CAAC,UAAU;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;", "names": ["document", "FocusMonitorDetectionMode", "window", "import_rxjs", "import_operators", "import_rxjs", "import_operators", "window", "HighContrastMode", "import_rxjs", "import_rxjs", "import_operators", "items", "import_rxjs", "import_rxjs", "import_operators", "import_rxjs", "import_operators"]}