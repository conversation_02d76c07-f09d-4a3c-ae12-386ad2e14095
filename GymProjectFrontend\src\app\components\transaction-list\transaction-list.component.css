/* Transaction List Component Styles */

.content-blur {
  filter: blur(2px);
  pointer-events: none;
}

/* Header Filters */
.transaction-filters {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Search Input */
.search-input-wrapper {
  position: relative;
  min-width: 250px;
}

.search-input {
  padding-left: 35px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.search-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
  background: var(--bg-primary);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Date Filters */
.date-filters {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-input-wrapper {
  position: relative;
  min-width: 140px;
}

.date-input {
  padding-left: 35px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.date-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

.date-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.clear-filter-btn {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-secondary);
  padding: 8px 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
}

.clear-filter-btn:hover {
  background: var(--danger);
  color: white;
  border-color: var(--danger);
}

/* View Toggle */
.transaction-view-selector {
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: center;
}

.view-toggle-container {
  display: flex;
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 4px;
  gap: 4px;
}

.view-toggle-btn {
  background: transparent;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  min-width: 120px;
  justify-content: center;
}

.view-toggle-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.view-toggle-btn.active {
  background: var(--primary);
  color: white;
  box-shadow: 0 2px 4px rgba(var(--primary-rgb), 0.3);
}

.count-badge {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  padding: 2px 6px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.view-toggle-btn:not(.active) .count-badge {
  background: var(--bg-quaternary);
  color: var(--text-secondary);
}

.view-toggle-btn.active .count-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Kompakt İşlem Özeti */
.transaction-summary {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
}

.summary-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 80px;
}

.stat-icon {
  color: var(--primary);
  font-size: 1.2rem;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Member Group Styling */
.member-group {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin-bottom: 20px;
  background: var(--bg-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.product-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.product-summary .badge {
  font-size: 0.75rem;
  background: var(--primary);
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 500;
}

/* Total Debt Styling */
.total-debt-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--bg-secondary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.total-debt-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.total-debt-amount {
  font-weight: 700;
  font-size: 1rem;
  color: var(--danger);
  letter-spacing: 0.5px;
}

/* Table Styling */
.table thead th {
  background: var(--bg-secondary);
  color: var(--text-primary);
  padding: 12px;
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
  font-size: 0.875rem;
}

.table td {
  vertical-align: middle;
  padding: 10px 12px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.badge {
  padding: 6px 10px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  font-weight: 500;
}

.badge:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

/* Dark Theme Support */
[data-theme="dark"] .search-input,
[data-theme="dark"] .date-input {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .search-input:focus,
[data-theme="dark"] .date-input:focus {
  border-color: var(--primary);
  background: var(--bg-primary);
}

[data-theme="dark"] .member-group {
  background: var(--bg-primary);
  border-color: var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .table thead th {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

[data-theme="dark"] .table td {
  color: var(--text-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .transaction-summary {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .transaction-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-group {
    width: 100%;
  }

  .search-input-wrapper,
  .date-input-wrapper {
    min-width: 100%;
  }

  .date-filters {
    flex-direction: column;
    gap: 8px;
  }

  .view-toggle-container {
    flex-direction: column;
    gap: 4px;
  }

  .view-toggle-btn {
    min-width: 100%;
    justify-content: space-between;
    padding: 12px 16px;
  }

  .product-summary {
    margin-bottom: 0.5rem;
  }

  .summary-stats {
    gap: 20px;
  }

  .stat-item {
    min-width: 60px;
  }

  .stat-value {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .modern-card-header {
    padding: 16px;
  }

  .transaction-view-selector {
    padding: 12px 16px;
  }

  .view-toggle-btn {
    font-size: 0.8rem;
    padding: 10px 12px;
  }
}

.segmented-control {
  display: inline-flex; /* Use inline-flex to wrap buttons */
  background-color: var(--bg-tertiary); /* Slightly different background */
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xs); /* Small padding around buttons */
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1); /* Inner shadow for depth */
}

.segment-button {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background-color: transparent; /* Initially transparent */
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  border-radius: var(--border-radius-sm); /* Slightly rounded corners for buttons */
  transition: background-color var(--transition-speed) var(--transition-timing),
              color var(--transition-speed) var(--transition-timing),
              box-shadow var(--transition-speed) var(--transition-timing);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 1; /* Make buttons take equal space if needed */
  text-align: center;
}

.segment-button:hover:not(.active) {
  background-color: rgba(var(--primary-rgb), 0.05); /* Subtle hover effect */
  color: var(--text-primary);
}

.segment-button.active {
  background-color: var(--white); /* Active button background */
  color: var(--primary); /* Active button text color */
  font-weight: 600;
  box-shadow: var(--shadow-sm); /* Shadow for active button */
}

.segment-button i {
  transition: color var(--transition-speed) var(--transition-timing);
}

/* Icon colors */
.segment-button.active i {
  color: var(--primary);
}

.segment-button:not(.active) i {
  color: var(--text-secondary);
}
.segment-button:hover:not(.active) i {
   color: var(--text-primary);
}


/* Dark Mode Adjustments for Segmented Control */
[data-theme="dark"] .segmented-control {
  background-color: var(--bg-secondary); /* Darker background for container */
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .segment-button {
  color: var(--text-secondary);
}

[data-theme="dark"] .segment-button:hover:not(.active) {
  background-color: rgba(var(--primary-rgb), 0.1); /* Adjusted hover for dark */
  color: var(--text-primary);
}

[data-theme="dark"] .segment-button.active {
  background-color: var(--bg-tertiary); /* Active button background in dark */
  color: var(--primary); /* Primary color adjusted for dark mode */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .segment-button.active i {
  color: var(--primary);
}

[data-theme="dark"] .segment-button:not(.active) i {
  color: var(--text-secondary);
}

[data-theme="dark"] .segment-button:hover:not(.active) i {
   color: var(--text-primary);
}