using Core.DataAccess;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface ISystemExerciseDal : IEntityRepository<SystemExercise>
    {
        List<SystemExerciseDto> GetAllSystemExercises();
        List<SystemExerciseDto> GetSystemExercisesByCategory(int categoryId);
        PaginatedResult<SystemExerciseDto> GetSystemExercisesFiltered(SystemExerciseFilterDto filter);
        List<SystemExerciseDto> SearchSystemExercises(string searchTerm);
        SystemExerciseDto GetSystemExerciseDetail(int exerciseId);
    }
}
